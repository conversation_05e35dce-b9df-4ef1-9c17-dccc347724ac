.PHONY : ci test setup cleanup

test :
	go test ./tests/terraform_test.go -v -count=1
setup :
	docker pull oraclelinux:8.1
	docker tag oraclelinux:8.1 localstack-ec2/oraclelinux8:ami-087a916d1e255e26e
	sudo pip3 install docker-compose

ci : setup
	${DOCKER_COMPOSE_PATH}docker-compose up --build --force-recreate --abort-on-container-exit

cleanup :
	${DOCKER_COMPOSE_PATH}docker-compose down
	docker container prune -f
	docker image prune -f
	docker rmi oraclelinux:8.1 localstack-ec2/oraclelinux8:ami-087a916d1e255e26e localstack/localstack-pro

fmt : 
	terraform fmt -recursive