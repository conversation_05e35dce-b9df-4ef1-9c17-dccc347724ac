resource "random_string" "aws_random" {
  length  = 10
  special = false
  upper   = false
}

locals {
  s3_bucket_tags             = merge(var.required_tags, { "DataCriticality" = "low", "DataSensitivity" = "low" })
  access_logging_bucket_name = "aws-observability-${random_string.aws_random.id}-access-logs"
  region_to_elb_account_id = {
    "us-east-1"      = "************",
    "us-east-2"      = "************",
    "us-west-1"      = "************",
    "us-west-2"      = "************",
    "af-south-1"     = "************",
    "ca-central-1"   = "************",
    "eu-central-1"   = "************",
    "eu-west-1"      = "************",
    "eu-west-2"      = "************",
    "eu-south-1"     = "************",
    "eu-west-3"      = "************",
    "eu-north-1"     = "************",
    "ap-east-1"      = "************",
    "ap-northeast-1" = "************",
    "ap-northeast-2" = "************",
    "ap-northeast-3" = "************",
    "ap-southeast-1" = "************",
    "ap-southeast-2" = "************",
    "ap-south-1"     = "************",
    "me-south-1"     = "************",
    "sa-east-1"      = "************",
    "us-gov-west-1"  = "************",
    "us-gov-east-1"  = "************",
    "cn-north-1"     = "************",
    "cn-northwest-1" = "************"
  }
}

resource "aws_s3_bucket" "access_logging_bucket" {
  bucket        = local.access_logging_bucket_name
  force_destroy = false
  tags          = local.s3_bucket_tags
}

resource "aws_s3_bucket_policy" "s3_bucket_policy" {
  bucket = aws_s3_bucket.access_logging_bucket.id
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::${local.region_to_elb_account_id[data.aws_region.this.name]}:root"
            },
            "Action": "s3:PutObject",
            "Resource": "arn:aws:s3:::${aws_s3_bucket.access_logging_bucket.id}/*"
        },
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "delivery.logs.amazonaws.com"
            },
            "Action": "s3:PutObject",
            "Resource": "arn:aws:s3:::${aws_s3_bucket.access_logging_bucket.id}/*",
            "Condition": {
                "StringEquals": {
                    "s3:x-amz-acl": "bucket-owner-full-control"
                }
            }
        },
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "delivery.logs.amazonaws.com"
            },
            "Action": "s3:GetBucketAcl",
            "Resource": "arn:aws:s3:::${aws_s3_bucket.access_logging_bucket.id}"
        }
    ]
}
EOF
}
