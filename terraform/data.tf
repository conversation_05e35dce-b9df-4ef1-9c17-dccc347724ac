data "aws_caller_identity" "this" {}

data "aws_region" "this" {}

data "aws_iam_roles" "administrator_access" {
  name_regex  = "AWSReservedSSO_AdministratorAccess_*"
  path_prefix = "/aws-reserved/sso.amazonaws.com/"
}

data "aws_iam_roles" "aws_administrator_access" {
  name_regex  = "AWSReservedSSO_AWSAdministratorAccess_*"
  path_prefix = "/aws-reserved/sso.amazonaws.com/"
}

data "aws_iam_roles" "aws_power_user_access" {
  name_regex  = "AWSReservedSSO_AWSPowerUserAccess_*"
  path_prefix = "/aws-reserved/sso.amazonaws.com/"
}

data "aws_iam_roles" "org_aws_administrator_access" {
  name_regex  = "AWSReservedSSO_AWSOrganizationsFullAccess_*"
  path_prefix = "/aws-reserved/sso.amazonaws.com/"
}

data "aws_iam_roles" "github_runner_role" {
  name_regex = ".*runner-role"
}

locals {
  kms_admins = concat(
    tolist(data.aws_iam_roles.administrator_access.arns),
    tolist(data.aws_iam_roles.aws_administrator_access.arns),
    tolist(data.aws_iam_roles.org_aws_administrator_access.arns),
    tolist(data.aws_iam_roles.aws_power_user_access.arns),
    tolist(data.aws_iam_roles.github_runner_role.arns)
  )
}

data "aws_iam_policy_document" "rds" {
  version = "2012-10-17"

  statement {
    sid    = "KMS CMK Management"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = local.kms_admins
    }
    actions   = ["kms:*"]
    resources = ["*"]
  }

  # This is a placeholder for other roles that may need access.
  statement {
    sid = "Allow RDS to use the key"
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:CreateGrant",
      "kms:DescribeKey"
    ]
    effect = "Allow"
    principals {
      type = "AWS"
      identifiers = [
        "*"
      ]
    }
    resources = ["*"]
    condition {
      test     = "StringEquals"
      variable = "kms:CallerAccount"
      values   = [data.aws_caller_identity.this.account_id]
    }
    condition {
      test     = "StringEquals"
      variable = "kms:ViaService"
      values   = [format("rds.%s.amazonaws.com", data.aws_region.this.name)]
    }
  }

  # if the key needs to shared externally we can use this section to provide another AWS account to share it with.
  statement {
    sid       = "Allow external account use of the customer managed key"
    effect    = "Allow"
    resources = ["*"]

    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey",
    ]

    principals {
      type        = "AWS"
      identifiers = concat(["arn:aws:iam::${data.aws_caller_identity.this.account_id}:root"], var.rds_kms_share_backup_account != "" ? [var.rds_kms_share_backup_account] : [])
    }
  }

  # if the key needs to shared externally we can use this section to provide another AWS account to share it with.
  statement {
    sid       = "Allow attachment of persistent resources in external account "
    effect    = "Allow"
    resources = ["*"]
    actions   = ["kms:CreateGrant"]

    principals {
      type        = "AWS"
      identifiers = concat(["arn:aws:iam::${data.aws_caller_identity.this.account_id}:root"], var.rds_kms_share_backup_account != "" ? [var.rds_kms_share_backup_account] : [])
    }
  }
}

data "aws_iam_policy_document" "secretsRO" {
  statement {
    sid    = ""
    effect = "Allow"
    # resources = aws_secretsmanager_secret.secrets.*.arn
    resources = [
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_rds_master"].arn,
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_rds_dxcore"].arn,
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_batman"].arn,
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_consul_bootstrap_token"].arn,
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_envoy_ssl_cert"].arn,
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_envoy_ssl_key"].arn,
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_one_zero_fix_route"].arn,
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_one_zero_quote_gate"].arn,
      aws_secretsmanager_secret.secrets["${var.required_tags.Platform}_rds_aurora_ro"].arn,
    ]

    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
      "secretsmanager:ListSecretVersionIds",
      "secretsmanager:ListSecrets"
    ]
  }

  statement {
    sid    = ""
    effect = "Allow"
    resources = [
      aws_kms_key.secrets.arn
    ]

    actions = [
      "kms:Decrypt"
    ]
  }
}

data "aws_security_groups" "strongdm" {
  count = var.strong_dm_enabled ? 1 : 0

  filter {
    name   = "group-name"
    values = ["StackSet-DeployStrongDMRelays*"]
  }
}


data "aws_iam_policy_document" "rds_pi" {
  version = "2012-10-17"

  statement {
    sid    = "KMS CMK Management"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.this.account_id}:root"]
    }
    actions   = ["kms:*"]
    resources = ["*"]
  }

  statement {
    sid       = "Allow viewing RDS Performance Insights"
    effect    = "Allow"
    resources = ["*"]

    actions = [
      "kms:Decrypt",
      "kms:GenerateDataKey",
    ]

    condition {
      test     = "StringEquals"
      variable = "kms:ViaService"
      values   = ["rds.region.amazonaws.com"]
    }

    condition {
      test     = "ForAnyValue:StringEquals"
      variable = "kms:EncryptionContext:aws:pi:service"
      values   = ["rds"]
    }

    condition {
      test     = "ForAnyValue:StringEquals"
      variable = "kms:EncryptionContext:service"
      values   = ["pi"]
    }

    principals {
      type = "AWS"
      # can be scoped down later
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.this.account_id}:root"]
    }
  }
}

data "aws_iam_policy_document" "all_servers_custom_iam_policy" {
  statement {
    sid       = "cloudwatchlogretention"
    effect    = "Allow"
    resources = ["*"]
    actions   = ["logs:PutRetentionPolicy"]
  }

  statement {
    sid       = "ConsulAndNomadAutoJoin"
    effect    = "Allow"
    resources = ["*"]
    actions   = ["ec2:DescribeInstances"]
  }

  # allow the instance to read and write to parameters store by path /centrum/${var.required_tags.Platform}/* 
  # and /centrum/${var.deployment_env}/*  <-- future path for environment specific parameters
  statement {
    sid    = "SsmParameters"
    effect = "Allow"
    resources = [
      "arn:aws:ssm:${data.aws_region.this.name}:${data.aws_caller_identity.this.account_id}:parameter/centrum/${var.required_tags.Platform}/*",
      "arn:aws:ssm:${data.aws_region.this.name}:${data.aws_caller_identity.this.account_id}:parameter/centrum/${var.deployment_env}/*"
    ]
    actions = [
      "ssm:PutParameter",
      "ssm:DeleteParameter",
      "ssm:RemoveTagsFromResource",
      "ssm:AddTagsToResource",
      "ssm:GetParametersByPath",
      "ssm:GetParameters",
      "ssm:GetParameter",
      "ssm:DeleteParameters",
      "ssm:ListTagsForResource"
    ]
  }
  statement {
    sid       = "SsmDescribeParameters"
    effect    = "Allow"
    resources = ["*"]
    actions = [
      "ssm:DescribeParameters"
    ]
  }
}
