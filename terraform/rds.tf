resource "random_password" "master" {
  length  = var.rds_master_pass_length
  special = false
}

#KMS Key

resource "aws_kms_key" "rds" {
  deletion_window_in_days = 30
  enable_key_rotation     = true
  policy                  = data.aws_iam_policy_document.rds.json
  description             = "RDS Kms key for ${var.deployment_env}"
}

resource "aws_kms_alias" "rds" {
  name          = "alias/rds-${var.required_tags.Platform}"
  target_key_id = aws_kms_key.rds.id
}

#RDSs

module "db" {
  source              = "terraform-aws-modules/rds-aurora/aws"
  version             = "9.3.1"
  name                = var.rds.name
  engine              = var.rds.engine
  engine_version      = var.rds.engine_version
  subnets             = tolist(data.aws_subnets.private_subnets.ids)
  vpc_id              = data.aws_vpc.existing.id
  instance_class      = var.rds.instance_type
  instances           = var.rds.instances
  kms_key_id          = aws_kms_key.rds.arn
  deletion_protection = try(var.rds.deletion_protection, false)
  ca_cert_identifier  = try(var.rds.ca_cert_identifier, "rds-ca-2019")

  db_subnet_group_name = "${var.deployment_env}-postgresql"

  performance_insights_enabled    = try(var.rds.performance_insights_enabled, false)
  performance_insights_kms_key_id = try(var.rds.performance_insights_enabled, false) == true ? aws_kms_key.rds_pi.arn : ""

  monitoring_interval = try(var.rds.monitoring_interval, 0)

  master_password = random_password.master.result
  # master_password             = random_password.passwords["${var.required_tags.Platform}_rds_master"].result
  master_username             = "root"
  manage_master_user_password = false

  apply_immediately                   = true
  skip_final_snapshot                 = true
  db_parameter_group_name             = aws_db_parameter_group.aurora_db_postgres12_parameter_group.id
  db_cluster_parameter_group_name     = aws_rds_cluster_parameter_group.aurora_cluster_postgres12_parameter_group.id
  enabled_cloudwatch_logs_exports     = ["postgresql"]
  security_group_description          = ""
  copy_tags_to_snapshot               = true
  auto_minor_version_upgrade          = var.rds.auto_minor_version_upgrade
  iam_database_authentication_enabled = try(var.rds.iam_database_authentication_enabled, false)
  snapshot_identifier                 = var.rds_snapshot_identifier
  tags                                = var.required_tags
}

resource "aws_db_parameter_group" "aurora_db_postgres12_parameter_group" {
  name        = "${var.deployment_env}-db-postgres12-parameter-group"
  family      = "aurora-postgresql12"
  description = "${var.deployment_env} parameter group"
  dynamic "parameter" {
    for_each = var.rds_params.ordinary_group
    content {

      name         = parameter.value["name"]
      value        = parameter.value["value"]
      apply_method = parameter.value["apply_method"]
    }
  }
  parameter {
    name         = "shared_preload_libraries"
    value        = var.rds.shared_preload_libraries
    apply_method = "pending-reboot"
  }
  tags = var.required_tags
}

resource "aws_rds_cluster_parameter_group" "aurora_cluster_postgres12_parameter_group" {
  name        = "${var.deployment_env}-postgres12-cluster-parameter-group"
  family      = "aurora-postgresql12"
  description = "${var.deployment_env} cluster parameter-group"
  dynamic "parameter" {
    for_each = var.rds_params.cluster_group
    content {

      name         = parameter.value["name"]
      value        = parameter.value["value"]
      apply_method = parameter.value["apply_method"]
    }
  }
  parameter {
    name         = "shared_preload_libraries"
    value        = var.rds.shared_preload_libraries
    apply_method = "pending-reboot"
  }

  tags = var.required_tags
}

resource "aws_route53_record" "writer" {
  zone_id = data.aws_route53_zone.zones.zone_id
  name    = "${var.required_tags.Platform}-rds-writer.${data.aws_route53_zone.zones.name}"
  type    = "CNAME"
  ttl     = 60
  records = [module.db.cluster_endpoint]
}

resource "aws_route53_record" "reader" {
  zone_id = data.aws_route53_zone.zones.zone_id
  name    = "${var.required_tags.Platform}-rds-reader.${data.aws_route53_zone.zones.name}"
  type    = "CNAME"
  ttl     = 60
  records = [module.db.cluster_reader_endpoint]
}

## Performance Insights KMS key

resource "aws_kms_key" "rds_pi" {
  description             = "RDS ${var.required_tags.Platform} PI"
  enable_key_rotation     = true
  deletion_window_in_days = 30
  policy                  = data.aws_iam_policy_document.rds_pi.json
}

resource "aws_kms_alias" "rds_pi" {
  name          = "alias/${var.required_tags.Platform}-rds-pi"
  target_key_id = aws_kms_key.rds_pi.key_id
}

resource "aws_db_subnet_group" "rds_subnet_group" {

  name        = "${var.deployment_env}-postgresql"
  description = "For Aurora cluster ${var.rds.name}-postgresql"
  subnet_ids  = tolist(data.aws_subnets.private_subnets.ids)

}
