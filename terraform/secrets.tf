locals {
  secrets = {
    "${var.required_tags.Platform}_smtp_email_password" = {
      "description"             = "Password to access to SMTP server for dxfx-reposrt service"
      "password"                = "replace-me"
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_rds_master" = {
      "description"             = "Master DB Password for DX Core Environment ${var.deployment_env}"
      "password"                = random_password.master.result
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_rds_dxcore" = {
      "description" = "DXCore User DB Password for DX Core Environment ${var.deployment_env}"
      # "password"                = generated with random_password.passwords below
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_batman" = {
      "description" = "Batman Password for DX Core Environment ${var.deployment_env}"
      # "password"                = generated with random_password.passwords below
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_consul_bootstrap_token" = {
      "description"             = "Consul Bootstrap Token for DX Core Environment ${var.deployment_env}"
      "password"                = "replace-me"
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_envoy_ssl_cert" = {
      "description"             = "Envoy proxy SSL Certificate"
      "password"                = data.aws_s3_object.envoy_ssl_cert.body
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_envoy_ssl_key" = {
      "description"             = "Envoy proxy SSL Key"
      "password"                = data.aws_s3_object.envoy_ssl_key.body
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_zabbix" = {
      "description" = "Zabbix Password for DX Core Environment ${var.deployment_env}"
      # "password"                = generated with random_password.passwords below
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_metricbeat" = {
      "description" = "MetricBeat Password for DX Core Environment ${var.deployment_env}"
      # "password"                = generated with random_password.passwords below
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_user_password" = {
      "description" = "DXConsole Password for DX Core Environment ${var.deployment_env}"
      # "password"                = generated with random_password.passwords below
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_elastic" = {
      "description" = "Elastic Password for DX Core Environment ${var.deployment_env}"
      # "password"                = generated with random_password.passwords below
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_kibana" = {
      "description" = "Kibana Password for DX Core Environment ${var.deployment_env}"
      # "password"                = generated with random_password.passwords below
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_one_zero_fix_route" = {
      "description"             = "One Zero Password for DX Core Environment ${var.deployment_env}"
      "password"                = "placeholder"
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_one_zero_quote_gate" = {
      "description"             = "One Zero Password for DX Core Environment ${var.deployment_env}"
      "password"                = "placeholder"
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_rds_chart_load" = {
      "description"             = "RDS chart_load Password for DX Core Environment ${var.deployment_env}"
      "password"                = random_password.rds_chart_load.result
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_rds_aurora_ro" = {
      "description"             = "RDS rds_aurora_ro Password for DX Core Environment ${var.deployment_env}"
      "password"                = random_password.rds_aurora_ro.result
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    },
    "${var.required_tags.Platform}_grafana_admin" = {
      "description"             = "grafana admin Password for DX Core Environment ${var.deployment_env}"
      "password"                = "admin"
      "recovery_window_in_days" = var.secrets_manager_recovery_window_days
    }
  }
}

resource "aws_secretsmanager_secret" "secrets" {
  for_each = local.secrets

  name                    = each.key
  description             = each.value["description"]
  kms_key_id              = aws_kms_key.secrets.arn
  recovery_window_in_days = each.value["recovery_window_in_days"]
}

resource "aws_secretsmanager_secret_version" "secrets" {
  for_each = aws_secretsmanager_secret.secrets

  secret_id     = each.value.id
  secret_string = try(local.secrets[each.key]["password"], random_password.passwords[each.key].result)
  lifecycle {
    ignore_changes = [secret_string, ]
  }
}

resource "random_password" "passwords" {
  for_each = { for k, v in local.secrets : k => v if !contains(keys(v), "password") }
  length   = 10
  special  = false
}

# ssh key
resource "aws_key_pair" "ssh_key" {
  # key_name   = "${var.required_tags.Project}-${var.deployment_env}-key" # to future change to use deployment environment name instead of tags
  key_name   = "${var.required_tags.Project}-${var.required_tags.Platform}-${var.required_tags.Env}-key"
  public_key = tls_private_key.ssh_key.public_key_openssh
}

resource "tls_private_key" "ssh_key" {
  algorithm = "RSA"
}

resource "aws_ssm_parameter" "ssm_private_key" {
  name  = "/dxtrade/${var.deployment_env}/private-key"
  value = tls_private_key.ssh_key.private_key_pem
  type  = "SecureString"
}

# access to secrets
data "aws_caller_identity" "current" {}

resource "aws_kms_key" "secrets" {
  description             = "RDS Secrets KMS Key for ${var.deployment_env}"
  deletion_window_in_days = 10
  enable_key_rotation     = true
  policy                  = <<HERE
{
  "Version" : "2012-10-17",
  "Id" : "auto-secretsmanager-1",
  "Statement" : [ {
    "Sid" : "Allow access through AWS Secrets Manager for all principals in the account that are authorized to use AWS Secrets Manager",
    "Effect" : "Allow",
    "Principal" : {
      "AWS" : "*"
    },
    "Action" : [ "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:CreateGrant", "kms:DescribeKey" ],
    "Resource" : "*",
    "Condition" : {
      "StringEquals" : {
        "kms:ViaService" : "secretsmanager.us-west-2.amazonaws.com",
        "kms:CallerAccount" : "${data.aws_caller_identity.current.account_id}"
      }
    }
  },{
    "Sid" : "Allow direct access to key metadata to the account",
    "Effect" : "Allow",
    "Principal" : {
      "AWS" : "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
    },
    "Action" : [ "kms:*" ],
    "Resource" : "*"
  } ]
}
HERE
}

## SSL Certificates ##
data "aws_s3_object" "envoy_ssl_cert" {
  provider = aws.virginia

  bucket = var.s3_ssl_bucket_name
  key    = var.s3_ssl_file_cert
}

data "aws_s3_object" "envoy_ssl_key" {
  provider = aws.virginia

  bucket = var.s3_ssl_bucket_name
  key    = var.s3_ssl_file_key
}

## RDS chart_load
resource "random_password" "rds_chart_load" {
  length  = 16
  special = false
}

## RDS Readonly user
resource "random_password" "rds_aurora_ro" {
  length  = 16
  special = false
}
