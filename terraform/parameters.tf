resource "terraform_data" "parameters_replacement" {
  input = [
    data.aws_ssm_parameter.app_ami_id,
    data.aws_ssm_parameter.elk_ami_id,
    data.aws_ssm_parameter.util_ami_id,
    data.aws_ssm_parameter.web_ami_id,
    data.aws_ssm_parameter.zabbix_ami_id,
  ]
}

# parameter store
# save parameters for Consul
resource "aws_ssm_parameter" "consul_parameters" {
  for_each = toset([
    "consul_bootstrap_token",
    "Nomad_token",
    "Read_Only_token",
    "Administrator_Token",
    "User_Token",
    "Consul_Agent_Token",
    "Zabbix_token",
    "Application_cluster_token",
    "Vault_auth_token",
  ])
  name        = "/centrum/${var.required_tags.Platform}/consul/${each.value}"
  description = "Tokens for consul env: ${var.deployment_env}"
  type        = "SecureString"
  value       = "replace-me"

  tags = var.required_tags

  # ignore all changes with parameters
  lifecycle {
    ignore_changes = all
    # recreate the parameter if instances change
    replace_triggered_by = [
      terraform_data.parameters_replacement
    ]
  }
}

resource "random_id" "consul_gossip" {
  byte_length = 32
}

resource "aws_ssm_parameter" "consul_gossip" {
  name        = "/centrum/${var.required_tags.Platform}/consul/consul_gossip"
  description = "Consul Gossip Encryption Key for environment: ${var.deployment_env}"
  type        = "SecureString"
  value       = random_id.consul_gossip.b64_std

  tags = var.required_tags

  # ignore all changes with parameters
  lifecycle {
    ignore_changes = all
  }
}

# save parameters for Nomad
resource "aws_ssm_parameter" "nomad_parameters" {
  for_each = toset([
    "nomad_bootstrap_flag",
    "nomad_bootstrap_token",
    "Ansible_Management_Token",
    "Vault_Token",
    "Support_Management_Token",
    "Support_RO_Token",
    "Support_DX_Token",
  ])
  name        = "/centrum/${var.required_tags.Platform}/nomad/${each.value}"
  description = "Tokens for nomad env: ${var.deployment_env}"
  type        = "SecureString"
  value       = "replace-me"

  tags = var.required_tags

  # ignore all changes with parameters
  lifecycle {
    ignore_changes = all
    # recreate the parameter if instances change
    replace_triggered_by = [
      terraform_data.parameters_replacement
    ]
  }

}

resource "random_id" "nomad_gossip" {
  byte_length = 32
}

resource "aws_ssm_parameter" "nomad_gossip" {
  name        = "/centrum/${var.required_tags.Platform}/nomad/nomad_gossip"
  description = "Nomad Gossip Encryption Key for environment: ${var.deployment_env}"
  type        = "SecureString"
  value       = random_id.nomad_gossip.b64_std

  tags = var.required_tags

  # ignore all changes with parameters
  lifecycle {
    ignore_changes = all
  }
}

# save parameters for ELK configuration
resource "aws_ssm_parameter" "metricbeat" {
  name        = "/centrum/${var.required_tags.Platform}/elastic/beats_system"
  description = "MetricBeat Password for DX Core Environment ${var.deployment_env}"
  type        = "SecureString"
  value       = random_password.passwords["${var.required_tags.Platform}_metricbeat"].result
  # value       = random_password.metricbeat.result

  tags = var.required_tags

  # ignore all changes with parameters
  lifecycle {
    ignore_changes = all
  }
}

# save parameters for Monitoring configuration
resource "aws_ssm_parameter" "rds" {
  for_each = {
    endpoint_address = aws_route53_record.writer.fqdn,
    database_name    = "dxcore"
    monitoring_user  = "zbx_monitor"
    # monitoring_password = random_password.dxcore.result
    monitoring_password = random_password.passwords["${var.required_tags.Platform}_rds_dxcore"].result
  }
  name        = "/centrum/${var.required_tags.Platform}/rds/${each.key}"
  description = "RDS ${each.key} for environment: ${var.deployment_env}"
  type        = "SecureString"
  value       = each.value

  tags = var.required_tags

  # ignore all changes with parameters
  lifecycle {
    ignore_changes = all
  }
}
