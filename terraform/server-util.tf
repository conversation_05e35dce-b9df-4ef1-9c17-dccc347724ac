# instance ami was created by packer and stored in aws ssm
data "aws_ssm_parameter" "util_ami_id" {
  name = "/centrum/${var.deployment_env}/ami-id"
}

module "util" {
  source = "./modules/terraform-aws-ec2-instance-5.2.1"
  #version = "5.2.1"

  count = var.util.count
  # insert the 10 required variables here
  # ami           = var.util.ami # this is the old way
  # try to use ami_id from var.pr_ami_id variable and if it is not set use ami_id from the SSM parameter store
  ami           = var.pr_ami_id != "" ? var.pr_ami_id : data.aws_ssm_parameter.util_ami_id.value
  instance_type = var.util.instance_type

  name                        = "${var.util.name}-${count.index + 1}"
  associate_public_ip_address = var.util.associate_public_ip_address
  key_name                    = aws_key_pair.ssh_key.key_name
  subnet_id                   = tolist(data.aws_subnets.private_subnets.ids)[count.index % length(tolist(data.aws_subnets.private_subnets.ids))]
  vpc_security_group_ids = [
    module.security-group.security_group_id,
    aws_security_group.ec2_instance_strongdm_sg.id,
  ]
  iam_instance_profile = aws_iam_instance_profile.util.name
  root_block_device    = var.util.root_block_device
  ebs_block_device     = var.util.ebs_block_device
  volume_tags = {
    "Env"      = var.required_tags.Env,
    "Platform" = var.required_tags.Platform,
    "Project"  = var.required_tags.Project,
  }
  tags = merge(
    var.required_tags,
    var.util.tags,
    { PscBackup = "true" },
    {
      "consul-server" = var.required_tags.Platform,
      "nomad-server"  = var.required_tags.Platform
    }
  )

  disable_api_termination = var.deletion_protection
  user_data = templatefile("${path.module}/${var.util.cloud_init}",
    {
      hostname            = "${var.util.name}${count.index + 1}",
      domain              = var.aws_local_domain,
      deploy_target       = var.deploy_target
      env_name            = var.required_tags.Platform
      AWS_REGION          = data.aws_region.this.name
      consul_servers      = var.util.count
      nomad_servers       = var.util.count
      elastic_host        = values(module.dynamic_records_elk[0].route53_record_fqdn)[0]
      project_ENVIRONMENT = var.project_ENVIRONMENT
      datacenter          = var.deployment_env
  })
  ebs_optimized = try(var.util.ebs_optimized, false)
}

### adds an A record to each private ip
# this solution is might not be ideal if you have a lot of them
# in our example we have 3 instances + 3 private interfaces so lists are element equal
#
# These dynamic records create internal DNS records which are used with Ansible inventory
########### Records dynamic
module "dynamic_records_util" {
  source  = "terraform-aws-modules/route53/aws//modules/records"
  version = "2.10.2"

  zone_id = data.aws_route53_zone.zones.id

  count = var.util.count
  records = [
    {
      name = "${var.util.name}${count.index + 1}"
      type = "A"
      ttl  = 3600
      records = [
        module.util[count.index].private_ip,
      ]
    }
  ]

}