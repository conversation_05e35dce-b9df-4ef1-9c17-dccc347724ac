resource "aws_iam_role" "app" {
  name               = "${var.deployment_env}-app-role"
  description        = "Allows EC2 instances to call AWS services on your behalf."
  assume_role_policy = <<HERE
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
HERE

}

resource "aws_iam_role_policy_attachment" "app" {
  for_each = {
    "AmazonSSMManagedInstanceCore"       = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    "AmazonEC2ContainerRegistryReadOnly" = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
    "AmazonS3ReadOnlyAccess"             = "arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess",
    "secretsRO"                          = aws_iam_policy.secretsRO.arn,
    "all_servers_custom_iam_policy"      = aws_iam_policy.all_servers_custom_iam_policy.arn,
    "CloudWatchAgentServerPolicy"        = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
  }

  role       = aws_iam_role.app.name
  policy_arn = each.value
}

resource "aws_iam_instance_profile" "app" {
  name = "${var.deployment_env}-app-ipf"
  role = aws_iam_role.app.name
}

resource "aws_iam_role" "web" {
  name               = "${var.deployment_env}-web-role"
  description        = "Allows EC2 instances to call AWS services on your behalf."
  assume_role_policy = <<HERE
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
HERE

}

resource "aws_iam_role_policy_attachment" "web" {
  for_each = {
    "AmazonSSMManagedInstanceCore"       = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    "AmazonEC2ContainerRegistryReadOnly" = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
    "AmazonS3ReadOnlyAccess"             = "arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess",
    "secretsRO"                          = aws_iam_policy.secretsRO.arn,
    "all_servers_custom_iam_policy"      = aws_iam_policy.all_servers_custom_iam_policy.arn,
    "CloudWatchAgentServerPolicy"        = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
  }

  role       = aws_iam_role.web.name
  policy_arn = each.value
}

resource "aws_iam_instance_profile" "web" {
  name = "${var.deployment_env}-web-ipf"
  role = aws_iam_role.web.name
}

resource "aws_iam_role" "util" {
  name               = "${var.deployment_env}-util-role"
  description        = "Allows EC2 instances to call AWS services on your behalf."
  assume_role_policy = <<HERE
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
HERE

}

resource "aws_iam_role_policy_attachment" "util" {
  for_each = {
    "AmazonSSMManagedInstanceCore"       = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    "AmazonEC2ContainerRegistryReadOnly" = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
    "secretsRO"                          = aws_iam_policy.secretsRO.arn,
    "all_servers_custom_iam_policy"      = aws_iam_policy.all_servers_custom_iam_policy.arn,
    "CloudWatchAgentServerPolicy"        = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
  }
  role       = aws_iam_role.util.name
  policy_arn = each.value
}

resource "aws_iam_instance_profile" "util" {
  name = "${var.deployment_env}-util-ipf"
  role = aws_iam_role.util.name
}

resource "aws_iam_role" "zabbix" {
  name               = "${var.deployment_env}-zabbix-role"
  description        = "Allows EC2 instances to call AWS services on your behalf."
  assume_role_policy = <<HERE
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
HERE

}

resource "aws_iam_role_policy_attachment" "zabbix" {
  for_each = {
    "AmazonSSMManagedInstanceCore"       = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    "AmazonEC2ContainerRegistryReadOnly" = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
    "AmazonS3ReadOnlyAccess"             = "arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess",
    "secretsRO"                          = aws_iam_policy.secretsRO.arn,
    "all_servers_custom_iam_policy"      = aws_iam_policy.all_servers_custom_iam_policy.arn,
    "CloudWatchAgentServerPolicy"        = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
  }
  role       = aws_iam_role.zabbix.name
  policy_arn = each.value
}

resource "aws_iam_instance_profile" "zabbix" {
  name = "${var.deployment_env}-zabbix-ipf"
  role = aws_iam_role.zabbix.name
}

resource "aws_iam_role" "elk" {
  name               = "${var.deployment_env}-elk-role"
  description        = "Allows EC2 instances to call AWS services on your behalf."
  assume_role_policy = <<HERE
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
HERE

}

resource "aws_iam_role_policy_attachment" "elk" {
  for_each = {
    "AmazonSSMManagedInstanceCore"       = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    "AmazonEC2ContainerRegistryReadOnly" = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
    "secretsRO"                          = aws_iam_policy.secretsRO.arn,
    "all_servers_custom_iam_policy"      = aws_iam_policy.all_servers_custom_iam_policy.arn,
    "CloudWatchAgentServerPolicy"        = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
  }
  role       = aws_iam_role.elk.name
  policy_arn = each.value
}

resource "aws_iam_instance_profile" "elk" {
  name = "${var.deployment_env}-elk-ipf"
  role = aws_iam_role.elk.name
}


resource "aws_iam_policy" "secretsRO" {
  description = "SecretsManager readonly permissions for ${var.deployment_env}"
  name        = "${var.deployment_env}_secrestRO"
  path        = "/"
  policy      = data.aws_iam_policy_document.secretsRO.json
}

resource "aws_iam_policy" "all_servers_custom_iam_policy" {
  description = "OMS Server custom policies permissions for ${var.deployment_env}"
  name        = "${var.deployment_env}_OMS_server_custom_policies"
  path        = "/"
  policy      = data.aws_iam_policy_document.all_servers_custom_iam_policy.json
}