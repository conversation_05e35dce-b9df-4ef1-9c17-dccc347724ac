resource "aws_lb" "admin_lb" {
  name                             = "${var.required_tags.Platform}-admin-alb"
  internal                         = false
  load_balancer_type               = "application"
  subnets                          = data.aws_subnets.public_subnets.ids
  security_groups                  = [aws_security_group.adminlb_sg.id]
  enable_deletion_protection       = false
  enable_cross_zone_load_balancing = true

  access_logs {
    bucket  = aws_s3_bucket.access_logging_bucket.id
    enabled = true
    prefix  = "${var.required_tags.Platform}-admin-alb"
  }

  depends_on = [
    aws_s3_bucket_policy.s3_bucket_policy
  ]

  tags = var.required_tags
}

data "aws_ec2_managed_prefix_list" "operations" {
  filter {
    name   = "prefix-list-name"
    values = ["psc-ops-office-vpn"]
  }
}

resource "aws_security_group" "adminlb_sg" {
  name        = "${var.required_tags.Platform} Admin LB"
  description = "Allow TLS inbound traffic"
  vpc_id      = data.aws_vpc.existing.id


  ingress {
    description     = "TLS from PST and DX"
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    prefix_list_ids = [data.aws_ec2_managed_prefix_list.operations.id]
  }

  ingress {
    description     = "HTTP from PST and DX"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    prefix_list_ids = [data.aws_ec2_managed_prefix_list.operations.id]
  }

  egress {
    from_port       = 4646
    to_port         = 4646
    protocol        = "tcp"
    security_groups = [module.security-group.security_group_id]
  }

  egress {
    from_port       = 8500
    to_port         = 8500
    protocol        = "tcp"
    security_groups = [module.security-group.security_group_id]
  }
  egress {
    from_port       = 5601
    to_port         = 5601
    protocol        = "tcp"
    security_groups = [module.security-group.security_group_id]
  }
  egress {
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = [module.security-group.security_group_id]
  }

  tags = var.required_tags
}


resource "aws_lb_target_group" "nomad" {
  name     = "${var.required_tags.Platform}-nomad"
  port     = 4646
  protocol = "HTTP"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTP"
    path     = "/ui"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}

resource "aws_lb_target_group" "consul" {
  name     = "${var.required_tags.Platform}-consul"
  port     = 8500
  protocol = "HTTP"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTP"
    path     = "/ui"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}

resource "aws_lb_target_group" "kibana" {
  name     = "${var.required_tags.Platform}-kibana"
  port     = 5601
  protocol = "HTTP"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTP"
    path     = "/"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}


resource "aws_lb_listener" "admin" {
  load_balancer_arn = aws_lb.admin_lb.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-FS-1-2-Res-2020-10"
  certificate_arn   = aws_acm_certificate.acm_wildcard.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nomad.arn
  }
  tags = var.required_tags
}

resource "aws_lb_listener" "https_redirect" {
  load_balancer_arn = aws_lb.admin_lb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener_rule" "nomad" {
  listener_arn = aws_lb_listener.admin.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nomad.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-nomad.${data.aws_route53_zone.external.name}"]
    }
  }
}

resource "aws_lb_listener_rule" "consul" {
  listener_arn = aws_lb_listener.admin.arn
  priority     = 110

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.consul.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-consul.${data.aws_route53_zone.external.name}"]
    }
  }
}


resource "aws_route53_record" "nomad" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-nomad.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb.dns_name
    zone_id                = aws_lb.admin_lb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "consul" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-consul.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb.dns_name
    zone_id                = aws_lb.admin_lb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group_attachment" "nomad_targets" {
  count            = var.util.count
  target_group_arn = aws_lb_target_group.nomad.arn
  target_id        = module.util[count.index].id
  port             = 4646
}

resource "aws_lb_target_group_attachment" "consul_targets" {
  count            = var.util.count
  target_group_arn = aws_lb_target_group.consul.arn
  target_id        = module.util[count.index].id
  port             = 8500
}

# ELK Kibana

resource "aws_lb_listener_rule" "kibana" {
  listener_arn = aws_lb_listener.admin.arn
  priority     = 120

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.kibana.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-kibana.${data.aws_route53_zone.external.name}"]
    }
  }
}

resource "aws_route53_record" "kibana" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-kibana.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb.dns_name
    zone_id                = aws_lb.admin_lb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group_attachment" "kibana_targets" {
  count            = var.elk.count
  target_group_arn = aws_lb_target_group.kibana.arn
  target_id        = module.elk[count.index].id
  port             = 5601
}


# Zabbix

resource "aws_lb_target_group" "zabbix" {
  name     = "${var.required_tags.Platform}-zabbix"
  port     = 443
  protocol = "HTTPS"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTPS"
    path     = "/"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}

resource "aws_lb_listener_rule" "zabbix" {
  listener_arn = aws_lb_listener.admin.arn
  priority     = 130

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.zabbix.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-zabbix.${data.aws_route53_zone.external.name}"]
    }
  }
}

resource "aws_route53_record" "zabbix" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-zabbix.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb.dns_name
    zone_id                = aws_lb.admin_lb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group_attachment" "zabbix_targets" {
  count            = var.elk.count
  target_group_arn = aws_lb_target_group.zabbix.arn
  target_id        = module.zabbix[count.index].id
  port             = 443
}


# Grafana

resource "aws_lb_target_group" "grafana" {
  name     = "${var.required_tags.Platform}-grafana"
  port     = 443
  protocol = "HTTPS"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTPS"
    path     = "/"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}

resource "aws_lb_listener_rule" "grafana" {
  listener_arn = aws_lb_listener.admin.arn
  priority     = 140

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.grafana.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-grafana.${data.aws_route53_zone.external.name}"]
    }
  }
}

resource "aws_route53_record" "grafana" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-grafana.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb.dns_name
    zone_id                = aws_lb.admin_lb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group_attachment" "grafana_targets" {
  count            = var.elk.count
  target_group_arn = aws_lb_target_group.grafana.arn
  target_id        = module.zabbix[count.index].id
  port             = 443
}
