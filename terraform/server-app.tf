# instance ami was created by packer and stored in aws ssm
data "aws_ssm_parameter" "app_ami_id" {
  name = "/centrum/${var.deployment_env}/ami-id"
}

module "app" {
  source = "./modules/terraform-aws-ec2-instance-5.2.1"
  #version = "5.2.1"

  count = var.app.count
  # insert the 10 required variables here
  # ami           = var.app.ami # this is the old way
  # try to use ami_id from var.pr_ami_id variable and if it is not set use ami_id from the SSM parameter store
  ami           = var.pr_ami_id != "" ? var.pr_ami_id : data.aws_ssm_parameter.app_ami_id.value
  instance_type = var.app.instance_type

  name                        = "${var.app.name}-${count.index + 1}"
  associate_public_ip_address = var.app.associate_public_ip_address
  key_name                    = aws_key_pair.ssh_key.key_name
  subnet_id                   = tolist(data.aws_subnets.private_subnets.ids)[count.index % length(tolist(data.aws_subnets.private_subnets.ids))]
  vpc_security_group_ids = [
    module.security-group.security_group_id,
    aws_security_group.ec2_instance_strongdm_sg.id,
  ]
  iam_instance_profile = aws_iam_instance_profile.app.name
  root_block_device    = var.app.root_block_device
  ebs_block_device     = var.app.ebs_block_device
  volume_tags = {
    "Env"      = var.required_tags.Env,
    "Platform" = var.required_tags.Platform,
    "Project"  = var.required_tags.Project,
  }
  tags = merge(
    var.required_tags,
    { PscBackup = "true" },
    var.app.tags,
  )

  user_data = templatefile("${path.module}/${var.app.cloud_init}",
    {
      hostname            = "${var.app.name}${count.index + 1}",
      domain              = var.aws_local_domain,
      deploy_target       = var.deploy_target
      env_name            = var.required_tags.Platform
      AWS_REGION          = data.aws_region.this.name
      consul_servers      = var.util.count
      nomad_servers       = var.util.count
      elastic_host        = values(module.dynamic_records_elk[0].route53_record_fqdn)[0]
      project_ENVIRONMENT = var.project_ENVIRONMENT
      datacenter          = var.deployment_env
  })

  ebs_optimized = try(var.app.ebs_optimized, false)
}

### adds an A record to each private ip
# this solution is might not be ideal if you have a lot of them
# in our example we have 3 instances + 3 private interfaces so lists are element equal
#
# These dynamic records create internal DNS records which are used with Ansible inventory
########### Records dynamic
module "dynamic_records_app" {
  source  = "terraform-aws-modules/route53/aws//modules/records"
  version = "2.10.2"

  zone_id = data.aws_route53_zone.zones.id

  count = var.app.count
  records = [
    {
      name = "${var.app.name}${count.index + 1}"
      type = "A"
      ttl  = 3600
      records = [
        module.app[count.index].private_ip,
      ]
    }
  ]
}

resource "aws_lb_target_group" "TG_app_ext" {

  count              = length(var.app_target_groups)
  name               = upper("${var.required_tags.Platform}-${var.app_target_groups[count.index].name}-ext")
  port               = var.app_target_groups[count.index].port
  protocol           = var.app_target_groups[count.index].protocol
  preserve_client_ip = var.app_target_groups[count.index].preserve_client_ip
  tags               = var.required_tags
  health_check {
    interval = 10
    protocol = "TCP"
  }
  target_type = "ip"
  vpc_id      = data.aws_vpc.existing.id
}

locals {
  app_product_ext = setproduct(aws_lb_target_group.TG_app_ext, module.app[*].private_ip)
}

resource "aws_lb_target_group_attachment" "app_LB_targets_ext" {
  count = length(local.app_product_ext)
  //noinspection HILUnresolvedReference
  target_group_arn = local.app_product_ext[count.index][0].arn
  //noinspection HILUnresolvedReference
  target_id = local.app_product_ext[count.index][1]
  //noinspection HILUnresolvedReference
  port = local.app_product_ext[count.index][0].port
}

resource "aws_lb_target_group" "TG_app_int" {

  count              = length(var.app_target_groups)
  name               = upper("${var.required_tags.Platform}-${var.app_target_groups[count.index].name}-int")
  port               = var.app_target_groups[count.index].port
  protocol           = var.app_target_groups[count.index].protocol
  preserve_client_ip = var.app_target_groups[count.index].preserve_client_ip
  tags               = var.required_tags
  health_check {
    interval = 10
    protocol = "TCP"
  }
  target_type = "ip"
  vpc_id      = data.aws_vpc.existing.id
}

locals {
  app_product_int = setproduct(aws_lb_target_group.TG_app_int, module.app[*].private_ip)
}

resource "aws_lb_target_group_attachment" "app_LB_targets_int" {
  count = length(local.app_product_int)
  //noinspection HILUnresolvedReference
  target_group_arn = local.app_product_int[count.index][0].arn
  //noinspection HILUnresolvedReference
  target_id = local.app_product_int[count.index][1]
  //noinspection HILUnresolvedReference
  port = local.app_product_int[count.index][0].port
}

#DO-1663 Add listeners and TG's to support multiplxor ingress
resource "aws_lb_target_group" "TG_multi_ext" {

  count              = var.app.count
  name               = upper("${var.required_tags.Platform}-multi${count.index}-ext")
  port               = 7501
  protocol           = "TCP"
  preserve_client_ip = true
  tags               = var.required_tags
  health_check {
    interval = 10
    protocol = "TCP"
  }
  target_type = "ip"
  vpc_id      = data.aws_vpc.existing.id
}
resource "aws_lb_target_group_attachment" "multi_targets_ext" {
  count = var.app.count
  //noinspection HILUnresolvedReference
  target_group_arn = aws_lb_target_group.TG_multi_ext[count.index].arn
  //noinspection HILUnresolvedReference
  target_id = module.app[count.index].private_ip
  //noinspection HILUnresolvedReference
  port = 7501
}

resource "aws_lb_listener" "app_lstnr_multi_ext" {
  load_balancer_arn = aws_lb.main_lb_ext.arn
  count             = var.app.count
  port              = 17001 + count.index
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.TG_multi_ext[count.index].arn
  }
  tags = var.required_tags
}