locals {
  elk_instances = [for instance in module.elk : {
    "id"            = instance.id
    "ami"           = instance.ami
    "instance_type" = instance.instance_type
    "volume_id"     = element(flatten(module.elk[*].ebs_block_device[*].volume_id), 0)
    "mount_point"   = "/opt"
    "keep_snapshot" = false
    "device"        = "nvme1n1p1"
    "fstype"        = "xfs"
    }
  ]
  app_instances = [for instance in module.app : {
    "id"            = instance.id
    "ami"           = instance.ami
    "instance_type" = instance.instance_type
    "volume_id"     = element(flatten(module.app[*].ebs_block_device[*].volume_id), 0)
    "mount_point"   = "/opt"
    "keep_snapshot" = false
    "device"        = "nvme1n1p1"
    "fstype"        = "xfs"
    }
  ]
  web_instances = [for instance in module.web : {
    "id"            = instance.id
    "ami"           = instance.ami
    "instance_type" = instance.instance_type
    "volume_id"     = element(flatten(module.web[*].ebs_block_device[*].volume_id), 0)
    "mount_point"   = "/opt"
    "keep_snapshot" = false
    "device"        = "nvme1n1p1"
    "fstype"        = "xfs"
    }
  ]
}

module "document_storage_resize" {
  source = "./modules/document-storage-resize"
  count  = var.elk_storage_alert_enabled || var.app_storage_alert_enabled || var.web_storage_alert_enabled ? 1 : 0

  ssm_document_name            = "${var.deployment_env}-PST-ExtendEbsVolume"
  email_notifications_endpoint = "<EMAIL>"
}


module "elk_storage_alert_resize" {
  source = "./modules/alarm-storage-resize"
  count  = var.elk_storage_alert_enabled ? length(local.elk_instances) : 0

  instance              = local.elk_instances[count.index]
  threshold             = 80
  alarm_actions         = [module.document_storage_resize[0].notification_topic_arn]
  aws_ssm_document_name = module.document_storage_resize[0].ssm_document_name
  events_iam_role_arm   = module.document_storage_resize[0].events_iam_role_arm
  ssm_document_iam_role = module.document_storage_resize[0].ssm_document_iam_role
}

module "app_storage_alert_resize" {
  source = "./modules/alarm-storage-resize"
  count  = var.app_storage_alert_enabled ? length(local.app_instances) : 0

  instance              = local.app_instances[count.index]
  threshold             = 80
  alarm_actions         = [module.document_storage_resize[0].notification_topic_arn]
  aws_ssm_document_name = module.document_storage_resize[0].ssm_document_name
  events_iam_role_arm   = module.document_storage_resize[0].events_iam_role_arm
  ssm_document_iam_role = module.document_storage_resize[0].ssm_document_iam_role
}

module "web_storage_alert_resize" {
  source = "./modules/alarm-storage-resize"
  count  = var.web_storage_alert_enabled ? length(local.web_instances) : 0

  instance              = local.web_instances[count.index]
  threshold             = 80
  alarm_actions         = [module.document_storage_resize[0].notification_topic_arn]
  aws_ssm_document_name = module.document_storage_resize[0].ssm_document_name
  events_iam_role_arm   = module.document_storage_resize[0].events_iam_role_arm
  ssm_document_iam_role = module.document_storage_resize[0].ssm_document_iam_role
}

