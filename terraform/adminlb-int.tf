resource "aws_lb" "admin_lb_int" {
  name                             = "${var.required_tags.Platform}-admin-alb-int"
  internal                         = true
  load_balancer_type               = "application"
  subnets                          = data.aws_subnets.private_subnets.ids
  security_groups                  = [aws_security_group.admin_lb_int_sg.id]
  enable_deletion_protection       = false
  enable_cross_zone_load_balancing = true

  access_logs {
    bucket  = aws_s3_bucket.access_logging_bucket.id
    enabled = true
    prefix  = "${var.required_tags.Platform}-admin-alb-int"
  }

  depends_on = [
    aws_s3_bucket_policy.s3_bucket_policy
  ]

  tags = var.required_tags
}

resource "aws_security_group" "admin_lb_int_sg" {
  name        = "${var.required_tags.Platform} Admin LB Int"
  description = "Allow TLS inbound traffic"
  vpc_id      = data.aws_vpc.existing.id

  ingress {
    description     = "TLS from Strong DM & VPC"
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = try(data.aws_security_groups.strongdm[0].ids, [])
    cidr_blocks     = [data.aws_vpc.existing.cidr_block]
  }

  ingress {
    description     = "HTTP from Strong DM & VPC"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = try(data.aws_security_groups.strongdm[0].ids, [])
    cidr_blocks     = [data.aws_vpc.existing.cidr_block]
  }

  ingress {
    description     = "StrongDM Debug Ports"
    from_port       = 5000
    to_port         = 6000
    protocol        = "tcp"
    security_groups = try(data.aws_security_groups.strongdm[0].ids, [])
    cidr_blocks     = [data.aws_vpc.existing.cidr_block]
  }

  ingress {
    description     = "StrongDM Debug Ports"
    from_port       = 15000
    to_port         = 16000
    protocol        = "tcp"
    security_groups = try(data.aws_security_groups.strongdm[0].ids, [])
    cidr_blocks     = [data.aws_vpc.existing.cidr_block]
  }

  egress {
    from_port       = 4646
    to_port         = 4646
    protocol        = "tcp"
    security_groups = [module.security-group.security_group_id]
  }

  egress {
    from_port       = 8500
    to_port         = 8500
    protocol        = "tcp"
    security_groups = [module.security-group.security_group_id]
  }
  egress {
    from_port       = 5601
    to_port         = 5601
    protocol        = "tcp"
    security_groups = [module.security-group.security_group_id]
  }
  egress {
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = [module.security-group.security_group_id]
  }

  tags = var.required_tags
}


resource "aws_lb_target_group" "nomad_int" {
  name     = "${var.required_tags.Platform}-nomad-int"
  port     = 4646
  protocol = "HTTP"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTP"
    path     = "/ui"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}

resource "aws_lb_target_group" "consul_int" {
  name     = "${var.required_tags.Platform}-consul-int"
  port     = 8500
  protocol = "HTTP"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTP"
    path     = "/ui"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}

resource "aws_lb_target_group" "kibana_int" {
  name     = "${var.required_tags.Platform}-kibana-int"
  port     = 5601
  protocol = "HTTP"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTP"
    path     = "/"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}


resource "aws_lb_listener" "admin_int" {
  load_balancer_arn = aws_lb.admin_lb_int.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-FS-1-2-Res-2020-10"
  certificate_arn   = aws_acm_certificate.acm_wildcard.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nomad_int.arn
  }
  tags = var.required_tags
}

resource "aws_lb_listener" "https_redirect_int" {
  load_balancer_arn = aws_lb.admin_lb_int.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener_rule" "nomad_int" {
  listener_arn = aws_lb_listener.admin_int.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nomad_int.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-nomad-int.${data.aws_route53_zone.external.name}"]
    }
  }
}

resource "aws_lb_listener_rule" "consul_int" {
  listener_arn = aws_lb_listener.admin_int.arn
  priority     = 110

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.consul_int.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-consul-int.${data.aws_route53_zone.external.name}"]
    }
  }
}


resource "aws_route53_record" "nomad_int" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-nomad-int.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb_int.dns_name
    zone_id                = aws_lb.admin_lb_int.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "consul_int" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-consul-int.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb_int.dns_name
    zone_id                = aws_lb.admin_lb_int.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group_attachment" "nomad_targets_int" {
  count            = var.util.count
  target_group_arn = aws_lb_target_group.nomad_int.arn
  target_id        = module.util[count.index].id
  port             = 4646
}

resource "aws_lb_target_group_attachment" "consul_targets_int" {
  count            = var.util.count
  target_group_arn = aws_lb_target_group.consul_int.arn
  target_id        = module.util[count.index].id
  port             = 8500
}

# ELK Kibana

resource "aws_lb_listener_rule" "kibana_int" {
  listener_arn = aws_lb_listener.admin_int.arn
  priority     = 120

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.kibana_int.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-kibana-int.${data.aws_route53_zone.external.name}"]
    }
  }
}

resource "aws_route53_record" "kibana_int" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-kibana-int.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb_int.dns_name
    zone_id                = aws_lb.admin_lb_int.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group_attachment" "kibana_targets_int" {
  count            = var.elk.count
  target_group_arn = aws_lb_target_group.kibana_int.arn
  target_id        = module.elk[count.index].id
  port             = 5601
}

# Zabbix

resource "aws_lb_target_group" "zabbix_int" {
  name     = "${var.required_tags.Platform}-zabbix-int"
  port     = 443
  protocol = "HTTPS"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTPS"
    path     = "/"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}


resource "aws_lb_listener_rule" "zabbix_int" {
  listener_arn = aws_lb_listener.admin_int.arn
  priority     = 130

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.zabbix_int.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-zabbix-int.${data.aws_route53_zone.external.name}"]
    }
  }
}

resource "aws_route53_record" "zabbix_int" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-zabbix-int.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb_int.dns_name
    zone_id                = aws_lb.admin_lb_int.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group_attachment" "zabbix_targets_int" {
  count            = var.elk.count
  target_group_arn = aws_lb_target_group.zabbix_int.arn
  target_id        = module.zabbix[count.index].id
  port             = 443
}

# grafana

resource "aws_lb_target_group" "grafana_int" {
  name     = "${var.required_tags.Platform}-grafana-int"
  port     = 443
  protocol = "HTTPS"
  tags     = var.required_tags
  health_check {
    interval = 10
    protocol = "HTTPS"
    path     = "/"
    matcher  = "200,300-310"
  }
  target_type = "instance"
  vpc_id      = data.aws_vpc.existing.id
}


resource "aws_lb_listener_rule" "grafana_int" {
  listener_arn = aws_lb_listener.admin_int.arn
  priority     = 140

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.grafana_int.arn
  }


  condition {
    host_header {
      values = ["${var.required_tags.Platform}-grafana-int.${data.aws_route53_zone.external.name}"]
    }
  }
}

resource "aws_route53_record" "grafana_int" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-grafana-int.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.admin_lb_int.dns_name
    zone_id                = aws_lb.admin_lb_int.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group_attachment" "grafana_targets_int" {
  count            = var.elk.count
  target_group_arn = aws_lb_target_group.grafana_int.arn
  target_id        = module.zabbix[count.index].id
  port             = 443
}
