#cloud-config
# vim: syntax=yaml
#
# ***********************
# 	---- for more examples look at: ------
# ---> https://cloudinit.readthedocs.io/en/latest/topics/examples.html
# ******************************
# 
# This YAML file contains the cloud-init configuration for the server with specific role.
# All packages and components already pre-installed in the AMI, so we just need to configure them.
# To generate configuration files for the components we use Ansible playbooks. Playbooks already pre-installed in the AMI.
# Steps executed by Ansible playbooks almost identical for all types of servers
# 
# We have several types of servers and we need to run different tags(tasks) for each type:
# - server - to run management tools like Consul server and Nomad server
#   Consul and Nomad:
#     - get_gossip_key - configure/get gossip key for Consul and Nomad
#     - configure - generate Consul and Nomad configuration files
#     - bootstrap - bootstrap Consul and Nomad, setup bootstrap configuration
#     - acl_setup - setup ACL for Consul and Nomad
#   Cloudwatch Agent, Filebeat, Metricbeat:
#     - configure - generate configuration files for Cloudwatch Agent, Filebeat, Metricbeat
# 
# - app/web agents to run the application containers. Their configuration identical, main difference is just role name in Nomad
#   Consul and Nomad:
#     - get_gossip_key - configure/get gossip key for Consul and Nomad
#     - configure - generate Consul and Nomad configuration files
#     - acl_agent_setup - only for Consul. setup ACL for Consul agent
#   Cloudwatch Agent, Filebeat, Metricbeat:
#     - configure - generate configuration files for Cloudwatch Agent, Filebeat, Metricbeat
# 
# - monitoring agent to run the monitoring tools like Zabbix, Grafana, Prometheus, etc. Also run metrics collection from RDS and other external sources. 
#   Consul and Nomad:
#     - get_gossip_key - configure/get gossip key for Consul and Nomad
#     - configure - generate Consul and Nomad configuration files
#     - acl_agent_setup - only for Consul. setup ACL for Consul agent
#   Cloudwatch Agent, Filebeat, Metricbeat:
#     - configure - generate configuration files for Cloudwatch Agent, Filebeat, Metricbeat
#
# - elk to run the ELK stack
#   Consul:
#     - get_gossip_key - configure/get gossip key for Consul and Nomad
#     - configure - generate Consul and Nomad configuration files
#     - acl_agent_setup - only for Consul. setup ACL for Consul agent
#   Cloudwatch Agent:
#     - configure - generate configuration files for Cloudwatch Agent
# 
# The are also playbooks and commands to extend partitions, set hostname, and configure iptables.
#
# Required environment variables are defined in the ansible_variables.yml file
# They are used in the playbooks to properly configure the components,
# for example:
# on server - consul and nomad server enabled and client disabled, 
# on app/web agents - consul and nomad client enabled and server disabled
# metricbeat_client_node enabled on all nodes where we collect metrics
# and metricbeat_monitoring_node enabled only on monitoring agent node
#
write_files:
- path: /opt/ansible/ansible_variables.yml
  owner: root:root
  permissions: '0644'
  content: |
    ############################################
    #
    # This file will be mounted to /ansible directory in the container
    # That is why we set @/ansible/ansible_variables.yml in the extra-vars, instead of /opt/ansible/ansible_variables.yml
    #
    ############################################
    #
    # Env setup
    env_name: ${env_name}
    AWS_REGION: ${AWS_REGION}
    
    # Project setup
    project_name: pst
    project_path: "/opt/project_{{ project_name }}"
    datacenter: ${datacenter}
    deploy_target: ${deploy_target}
    
    # Consul setup
    consul_servers: ${consul_servers}
    consul_server: true
    
    # Nomad setup
    nomad_servers: ${nomad_servers}
    nomad_client_enabled: false
    nomad_server_enabled: true
    
    # Cloudwatch Agent setup
    # Filebeat setup
    elastic_host: ${elastic_host}

    # Metricbeat setup
    metricbeat_client_node: true
    metricbeat_monitoring_node: false
    project_ENVIRONMENT: ${project_ENVIRONMENT}


runcmd:
  - 'hostnamectl set-hostname ${hostname}'
  - echo env_name=${env_name}
  - echo deploy_target=${deploy_target}
  - echo AWS_REGION=${AWS_REGION}
  - sudo iptables -F INPUT
  - echo extend partitions
  - docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_extend_storage.yml
  - echo Consul setup
  - echo docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_hashicorp_deploy_consul.yml --extra-vars "@/ansible/ansible_variables.yml" --tags get_gossip_key,configure,bootstrap,acl_setup
  - docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_hashicorp_deploy_consul.yml --extra-vars "@/ansible/ansible_variables.yml" --tags get_gossip_key,configure,bootstrap,acl_setup
  - echo Nomad setup
  - echo docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_hashicorp_deploy_nomad.yml --extra-vars "@/ansible/ansible_variables.yml" --tags get_gossip_key,configure,bootstrap,acl_setup
  - docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_hashicorp_deploy_nomad.yml --extra-vars "@/ansible/ansible_variables.yml" --tags get_gossip_key,configure,bootstrap,acl_setup
  - echo Cloudwatch Agent setup
  - echo docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_install_cloudwatch_agent.yml --extra-vars "@/ansible/ansible_variables.yml" --tags configure
  - docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_install_cloudwatch_agent.yml --extra-vars "@/ansible/ansible_variables.yml" --tags configure
  - echo Filebeat setup
  - echo docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_deploy_filebeat.yml --extra-vars "@/ansible/ansible_variables.yml" --tags configure
  - docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_deploy_filebeat.yml --extra-vars "@/ansible/ansible_variables.yml" --tags configure
  - echo Metricbeat setup
  - echo docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_deploy_metricbeat.yml --extra-vars "@/ansible/ansible_variables.yml" --tags configure
  - docker run -v /opt/ansible:/ansible --rm --network host localhost/ansible:latest -i /ansible/inventory /ansible/playbook_deploy_metricbeat.yml --extra-vars "@/ansible/ansible_variables.yml" --tags configure


preserve_hostname: true
fqdn: '${hostname}.${domain}'
hostname: '${hostname}'
