variable "security_groups" {
  type    = any
  default = {}
}

variable "required_tags" {
  type    = map(string)
  default = {}
}

variable "deployment_env" {
  type        = string
  description = "Deployment environment full name. For example: dev1, dev2, stg-us-demo, prd-uk-live, etc."
}

variable "pr_ami_id" {
  type        = string
  default     = ""
  description = "AMI ID for the Pull Request validation. If not set, it will be taken from the SSM parameter store."
}

variable "rds" {
  type    = any
  default = {}
}

variable "rds_params" {
  type    = any
  default = { ordinary_group : [], cluster_group : [] }
}

variable "rds_snapshot_identifier" {
  description = "Specifies whether or not to create this cluster from a snapshot. You can use either the name or ARN when specifying a DB cluster snapshot, or the ARN when specifying a DB snapshot"
  type        = string
  default     = null
}

variable "util" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
  })
}

variable "app" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}

variable "app_storage_alert_enabled" {
  type        = bool
  default     = false
  description = "Enable or disable the alarm for the APP storage resize"
}

variable "web" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}

variable "web_storage_alert_enabled" {
  type        = bool
  default     = false
  description = "Enable or disable the alarm for the WEB storage resize"
}

variable "zabbix" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}

variable "elk" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}

variable "elk_storage_alert_enabled" {
  type        = bool
  default     = false
  description = "Enable or disable the alarm for the ELK storage resize"
}

variable "app_target_groups" {
  type    = list(map(string))
  default = []
}

variable "web_target_groups" {
  type    = list(map(string))
  default = []
}

variable "aws_local_domain" {
  type        = string
  default     = "aws.local"
  description = "main domain for route_53"
}

variable "aws_external_domain" {
  type        = string
  description = "external domain for route_53"
}

variable "vpc_name" {
  type = string
}

# Warning: [Fixable] variable "subnet_db" is declared but not used (terraform_unused_declarations)
# variable "subnet_db" {
#   type = string
# }
variable "subnet_private" {
  type = string
}
variable "subnet_public" {
  type = string
}

variable "secrets_manager_recovery_window_days" {
  description = "Secret manager recovery window in days. Can be set to 0 for allowing immediate deletes."
  type        = number
  default     = 30
}

variable "rds_master_pass_length" {
  description = "length of the randonmly generated password for the rds master account."
  type        = number
  default     = 24
  sensitive   = true
}

variable "s3_ssl_bucket_name" {
  description = "Manual s3 bucket name for ssl certificates to be pulled from."
  type        = string
  default     = ""
}

variable "s3_ssl_file_cert" {
  description = "Manual s3 cert file location."
  type        = string
  default     = ""
}

variable "s3_ssl_file_key" {
  description = "Manual s3 cert file location."
  type        = string
  default     = ""
}

variable "strong_dm_enabled" {
  type    = bool
  default = true
}

variable "deletion_protection" {
  type    = bool
  default = false
}

variable "rds_kms_share_backup_account" {
  description = "rds kms key need to share with central backup account for cross account backup, use Audit account for prd env and cloudops dev account for stg/dev env"
  type        = string
  default     = "arn:aws:iam::************:root"
}

# Cloudwatch logs
variable "app_logs_retention_in_days" {
  description = "The number of days you want to retain log events in the application log group."
  type        = number
  default     = 0
}

variable "instance_logs_retention_in_days" {
  description = "The number of days you want to retain log events in the instance log group."
  type        = number
  default     = 0
}

# These variables need to be reviewd and removed or updated if necessarry
variable "deploy_target" {
  description = "The target something (for ex. environment) to deploy to. deploy_target"
  type        = string
  default     = "pst_qa"
}

variable "project_ENVIRONMENT" {
  description = "Another target something to deploy to. Used: QA or PROD. project_ENVIRONMENT"
  type        = string
  default     = "QA"
}