# ec2 instance sg

module "security-group" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "5.1.0"
  # name                     = var.deployment_env # to future change to use deployment environment name instead of tags
  name                     = var.required_tags.Platform
  vpc_id                   = data.aws_vpc.existing.id
  ingress_with_cidr_blocks = var.security_groups.ingress_with_cidr_blocks
  ingress_cidr_blocks      = var.security_groups.ingress_cidr_blocks
  ingress_rules            = var.security_groups.ingress_rules
  egress_rules             = var.security_groups.egress_rules
  tags                     = var.required_tags
}

# ec2 instance strongdm port 22

resource "aws_security_group" "ec2_instance_strongdm_sg" {
  name        = "${var.deployment_env} SSH StrongDM"
  description = "SSH StrongDM traffic"
  vpc_id      = data.aws_vpc.existing.id

  ingress {
    description     = "SSH from Strong DM"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = try(data.aws_security_groups.strongdm[0].ids, [])
  }

  ingress {
    description     = "StrongDM ingress Ports"
    from_port       = 5000
    to_port         = 6000
    protocol        = "tcp"
    security_groups = try(data.aws_security_groups.strongdm[0].ids, [])
    cidr_blocks     = [data.aws_vpc.existing.cidr_block]
  }

  ingress {
    description     = "StrongDM ingress Ports"
    from_port       = 15000
    to_port         = 16000
    protocol        = "tcp"
    security_groups = try(data.aws_security_groups.strongdm[0].ids, [])
    cidr_blocks     = [data.aws_vpc.existing.cidr_block]
  }

  tags = var.required_tags
}


#############
# RDS Aurora
#############

resource "aws_security_group_rule" "allow_access" {
  description              = "dxtrade-rds-access"
  type                     = "ingress"
  from_port                = module.db.cluster_port
  to_port                  = module.db.cluster_port
  protocol                 = "tcp"
  source_security_group_id = module.security-group.security_group_id
  security_group_id        = module.db.security_group_id
}

# Strong DM RDS access (postgres)
# Foeach loop will not run if there are no entries in the data lookup for the strong dm security group

resource "aws_security_group_rule" "strong_dm_allow_access" {

  for_each = toset(try(data.aws_security_groups.strongdm[0].ids, []))

  description              = "strongdm rds"
  type                     = "ingress"
  from_port                = module.db.cluster_port
  to_port                  = module.db.cluster_port
  protocol                 = "tcp"
  source_security_group_id = each.value
  security_group_id        = module.db.security_group_id
}

data "aws_route53_zone" "zones" {
  name         = var.aws_local_domain
  private_zone = true
}

data "aws_route53_zone" "external" {
  name         = var.aws_external_domain
  private_zone = false
}

resource "aws_route53_record" "lbext" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.main_lb_ext.dns_name
    zone_id                = aws_lb.main_lb_ext.zone_id
    evaluate_target_health = true
  }
}

#
# External Network Load Balancer
#
resource "aws_eip" "external_lb" {
  count = length(data.aws_subnets.public_subnets.ids)

}

locals {

  // we look through subnets and elastic ips to make a map
  subnet_eip_map = [for index, subnet in data.aws_subnets.public_subnets.ids :
    {
      "eip" : aws_eip.external_lb[index].id,
      "subnet" : subnet
    }
  ]

}

resource "aws_lb" "main_lb_ext" {
  name                             = "${var.required_tags.Platform}-lb-ext"
  internal                         = false
  load_balancer_type               = "network"
  enable_deletion_protection       = var.deletion_protection
  enable_cross_zone_load_balancing = true

  dynamic "subnet_mapping" {
    for_each = local.subnet_eip_map
    content {
      subnet_id     = subnet_mapping.value.subnet
      allocation_id = subnet_mapping.value.eip
    }
  }

  access_logs {
    bucket  = aws_s3_bucket.access_logging_bucket.id
    enabled = true
    prefix  = "${var.required_tags.Platform}-lb-ext"
  }

  depends_on = [
    aws_s3_bucket_policy.s3_bucket_policy
  ]

  tags = var.required_tags
}

# End of External Network Load Balancer


resource "aws_lb_listener" "app_lstnr_ext" {
  load_balancer_arn = aws_lb.main_lb_ext.arn
  count             = length(var.app_target_groups)
  port              = var.app_target_groups[count.index].port
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.TG_app_ext[count.index].arn
  }
  tags = var.required_tags
}

resource "aws_lb_listener" "web_lstnr_ext" {
  load_balancer_arn = aws_lb.main_lb_ext.arn
  count             = length(var.web_target_groups)
  port              = var.web_target_groups[count.index].port
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.TG_web_ext[count.index].arn
  }
  tags = var.required_tags
}

resource "aws_route53_record" "lbint" {
  zone_id = data.aws_route53_zone.external.zone_id
  name    = "${var.required_tags.Platform}-int.${data.aws_route53_zone.external.name}"
  type    = "A"
  alias {
    name                   = aws_lb.main_lb_int.dns_name
    zone_id                = aws_lb.main_lb_int.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb" "main_lb_int" {
  name               = "${var.required_tags.Platform}-lb-int"
  internal           = true
  load_balancer_type = "network"
  subnets            = data.aws_subnets.private_subnets.ids

  enable_deletion_protection       = var.deletion_protection
  enable_cross_zone_load_balancing = true

  access_logs {
    bucket  = aws_s3_bucket.access_logging_bucket.id
    enabled = true
    prefix  = "${var.required_tags.Platform}-lb-int"
  }

  depends_on = [
    aws_s3_bucket_policy.s3_bucket_policy
  ]

  tags = var.required_tags
}


resource "aws_lb_listener" "app_lstnr_int" {
  load_balancer_arn = aws_lb.main_lb_int.arn
  count             = length(var.app_target_groups)
  port              = var.app_target_groups[count.index].port
  protocol          = "TCP"


  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.TG_app_int[count.index].arn
  }
  tags = var.required_tags
}

resource "aws_lb_listener" "web_lstnr_int" {
  load_balancer_arn = aws_lb.main_lb_int.arn
  count             = length(var.web_target_groups)
  port              = var.web_target_groups[count.index].port
  protocol          = "TCP"


  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.TG_web_int[count.index].arn
  }
  tags = var.required_tags
}