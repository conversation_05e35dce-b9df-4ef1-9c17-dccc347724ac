<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0, < 1.6.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |
| <a name="requirement_random"></a> [random](#requirement\_random) | ~> 3.0 |
| <a name="requirement_tls"></a> [tls](#requirement\_tls) | 4.0.5 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |
| <a name="provider_aws.virginia"></a> [aws.virginia](#provider\_aws.virginia) | ~> 5.0 |
| <a name="provider_random"></a> [random](#provider\_random) | ~> 3.0 |
| <a name="provider_terraform"></a> [terraform](#provider\_terraform) | n/a |
| <a name="provider_tls"></a> [tls](#provider\_tls) | 4.0.5 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_app"></a> [app](#module\_app) | ./modules/terraform-aws-ec2-instance-5.2.1 | n/a |
| <a name="module_app_storage_alert_resize"></a> [app\_storage\_alert\_resize](#module\_app\_storage\_alert\_resize) | ./modules/alarm-storage-resize | n/a |
| <a name="module_db"></a> [db](#module\_db) | terraform-aws-modules/rds-aurora/aws | 9.3.1 |
| <a name="module_document_storage_resize"></a> [document\_storage\_resize](#module\_document\_storage\_resize) | ./modules/document-storage-resize | n/a |
| <a name="module_dynamic_records_app"></a> [dynamic\_records\_app](#module\_dynamic\_records\_app) | terraform-aws-modules/route53/aws//modules/records | 2.10.2 |
| <a name="module_dynamic_records_elk"></a> [dynamic\_records\_elk](#module\_dynamic\_records\_elk) | terraform-aws-modules/route53/aws//modules/records | 2.10.2 |
| <a name="module_dynamic_records_util"></a> [dynamic\_records\_util](#module\_dynamic\_records\_util) | terraform-aws-modules/route53/aws//modules/records | 2.10.2 |
| <a name="module_dynamic_records_web"></a> [dynamic\_records\_web](#module\_dynamic\_records\_web) | terraform-aws-modules/route53/aws//modules/records | 2.10.2 |
| <a name="module_dynamic_records_zabbix"></a> [dynamic\_records\_zabbix](#module\_dynamic\_records\_zabbix) | terraform-aws-modules/route53/aws//modules/records | 2.10.2 |
| <a name="module_elk"></a> [elk](#module\_elk) | ./modules/terraform-aws-ec2-instance-5.2.1 | n/a |
| <a name="module_elk_storage_alert_resize"></a> [elk\_storage\_alert\_resize](#module\_elk\_storage\_alert\_resize) | ./modules/alarm-storage-resize | n/a |
| <a name="module_security-group"></a> [security-group](#module\_security-group) | terraform-aws-modules/security-group/aws | 5.1.0 |
| <a name="module_util"></a> [util](#module\_util) | ./modules/terraform-aws-ec2-instance-5.2.1 | n/a |
| <a name="module_web"></a> [web](#module\_web) | ./modules/terraform-aws-ec2-instance-5.2.1 | n/a |
| <a name="module_web_storage_alert_resize"></a> [web\_storage\_alert\_resize](#module\_web\_storage\_alert\_resize) | ./modules/alarm-storage-resize | n/a |
| <a name="module_zabbix"></a> [zabbix](#module\_zabbix) | ./modules/terraform-aws-ec2-instance-5.2.1 | n/a |

## Resources

| Name | Type |
|------|------|
| [aws_acm_certificate.acm_wildcard](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/acm_certificate) | resource |
| [aws_acm_certificate_validation.acm_wildcard](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/acm_certificate_validation) | resource |
| [aws_cloudwatch_log_group.app](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group) | resource |
| [aws_cloudwatch_log_group.instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group) | resource |
| [aws_db_parameter_group.aurora_db_postgres12_parameter_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_parameter_group) | resource |
| [aws_db_subnet_group.rds_subnet_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_subnet_group) | resource |
| [aws_eip.external_lb](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eip) | resource |
| [aws_iam_instance_profile.app](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_instance_profile.elk](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_instance_profile.util](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_instance_profile.web](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_instance_profile.zabbix](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_policy.all_servers_custom_iam_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.secretsRO](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_role.app](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.elk](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.util](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.web](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.zabbix](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.app](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.elk](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.util](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.web](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.zabbix](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_key_pair.ssh_key](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/key_pair) | resource |
| [aws_kms_alias.rds](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_alias) | resource |
| [aws_kms_alias.rds_pi](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_alias) | resource |
| [aws_kms_key.rds](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key) | resource |
| [aws_kms_key.rds_pi](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key) | resource |
| [aws_kms_key.secrets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key) | resource |
| [aws_lb.admin_lb](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb) | resource |
| [aws_lb.admin_lb_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb) | resource |
| [aws_lb.main_lb_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb) | resource |
| [aws_lb.main_lb_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb) | resource |
| [aws_lb_listener.admin](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.admin_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.app_lstnr_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.app_lstnr_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.app_lstnr_multi_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.https_redirect](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.https_redirect_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.web_lstnr_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.web_lstnr_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener_rule.consul](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.consul_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.grafana](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.grafana_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.kibana](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.kibana_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.nomad](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.nomad_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.zabbix](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.zabbix_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |
| [aws_lb_target_group.TG_app_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.TG_app_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.TG_multi_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.TG_web_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.TG_web_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.consul](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.consul_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.grafana](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.grafana_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.kibana](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.kibana_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.nomad](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.nomad_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.zabbix](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.zabbix_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group_attachment.app_LB_targets_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.app_LB_targets_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.consul_targets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.consul_targets_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.grafana_targets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.grafana_targets_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.kibana_targets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.kibana_targets_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.multi_targets_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.nomad_targets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.nomad_targets_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.web_LB_targets_ext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.web_LB_targets_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.zabbix_targets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_lb_target_group_attachment.zabbix_targets_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_rds_cluster_parameter_group.aurora_cluster_postgres12_parameter_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster_parameter_group) | resource |
| [aws_route53_record.acm_wildcard](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.consul](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.consul_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.grafana](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.grafana_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.kibana](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.kibana_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.lbext](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.lbint](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.nomad](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.nomad_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.reader](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.writer](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.zabbix](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.zabbix_int](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_s3_bucket.access_logging_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket) | resource |
| [aws_s3_bucket_policy.s3_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy) | resource |
| [aws_secretsmanager_secret.secrets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret) | resource |
| [aws_secretsmanager_secret_version.secrets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret_version) | resource |
| [aws_security_group.admin_lb_int_sg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group.adminlb_sg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group.ec2_instance_strongdm_sg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group_rule.allow_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.strong_dm_allow_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_ssm_parameter.consul_gossip](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_ssm_parameter.consul_parameters](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_ssm_parameter.metricbeat](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_ssm_parameter.nomad_gossip](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_ssm_parameter.nomad_parameters](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_ssm_parameter.rds](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_ssm_parameter.ssm_private_key](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [random_id.consul_gossip](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/id) | resource |
| [random_id.nomad_gossip](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/id) | resource |
| [random_password.master](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/password) | resource |
| [random_password.passwords](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/password) | resource |
| [random_password.rds_aurora_ro](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/password) | resource |
| [random_password.rds_chart_load](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/password) | resource |
| [random_string.aws_random](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/string) | resource |
| [terraform_data.parameters_replacement](https://registry.terraform.io/providers/hashicorp/terraform/latest/docs/resources/data) | resource |
| [tls_private_key.ssh_key](https://registry.terraform.io/providers/hashicorp/tls/4.0.5/docs/resources/private_key) | resource |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_caller_identity.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_ec2_managed_prefix_list.operations](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ec2_managed_prefix_list) | data source |
| [aws_iam_policy_document.all_servers_custom_iam_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.rds](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.rds_pi](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.secretsRO](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_roles.administrator_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_roles) | data source |
| [aws_iam_roles.aws_administrator_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_roles) | data source |
| [aws_iam_roles.aws_power_user_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_roles) | data source |
| [aws_iam_roles.github_runner_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_roles) | data source |
| [aws_iam_roles.org_aws_administrator_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_roles) | data source |
| [aws_region.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |
| [aws_route53_zone.external](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/route53_zone) | data source |
| [aws_route53_zone.zones](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/route53_zone) | data source |
| [aws_s3_object.envoy_ssl_cert](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/s3_object) | data source |
| [aws_s3_object.envoy_ssl_key](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/s3_object) | data source |
| [aws_security_groups.strongdm](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/security_groups) | data source |
| [aws_ssm_parameter.app_ami_id](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ssm_parameter) | data source |
| [aws_ssm_parameter.elk_ami_id](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ssm_parameter) | data source |
| [aws_ssm_parameter.util_ami_id](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ssm_parameter) | data source |
| [aws_ssm_parameter.web_ami_id](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ssm_parameter) | data source |
| [aws_ssm_parameter.zabbix_ami_id](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ssm_parameter) | data source |
| [aws_subnets.private_subnets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnets) | data source |
| [aws_subnets.public_subnets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnets) | data source |
| [aws_vpc.existing](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/vpc) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_app"></a> [app](#input\_app) | n/a | <pre>object({<br>    name                        = string,<br>    instance_type               = string,<br>    count                       = number,<br>    ami                         = string,<br>    associate_public_ip_address = bool<br>    eip                         = bool<br>    root_block_device           = list(any)<br>    ebs_block_device            = list(any)<br>    cloud_init                  = string<br>    tags                        = map(string)<br>    ebs_optimized               = bool<br>  })</pre> | n/a | yes |
| <a name="input_app_logs_retention_in_days"></a> [app\_logs\_retention\_in\_days](#input\_app\_logs\_retention\_in\_days) | The number of days you want to retain log events in the application log group. | `number` | `0` | no |
| <a name="input_app_storage_alert_enabled"></a> [app\_storage\_alert\_enabled](#input\_app\_storage\_alert\_enabled) | Enable or disable the alarm for the APP storage resize | `bool` | `false` | no |
| <a name="input_app_target_groups"></a> [app\_target\_groups](#input\_app\_target\_groups) | n/a | `list(map(string))` | `[]` | no |
| <a name="input_aws_external_domain"></a> [aws\_external\_domain](#input\_aws\_external\_domain) | external domain for route\_53 | `string` | n/a | yes |
| <a name="input_aws_local_domain"></a> [aws\_local\_domain](#input\_aws\_local\_domain) | main domain for route\_53 | `string` | `"aws.local"` | no |
| <a name="input_deletion_protection"></a> [deletion\_protection](#input\_deletion\_protection) | n/a | `bool` | `false` | no |
| <a name="input_deploy_target"></a> [deploy\_target](#input\_deploy\_target) | The target something (for ex. environment) to deploy to. deploy\_target | `string` | `"pst_qa"` | no |
| <a name="input_deployment_env"></a> [deployment\_env](#input\_deployment\_env) | Deployment environment full name. For example: dev1, dev2, stg-us-demo, prd-uk-live, etc. | `string` | n/a | yes |
| <a name="input_elk"></a> [elk](#input\_elk) | n/a | <pre>object({<br>    name                        = string,<br>    instance_type               = string,<br>    count                       = number,<br>    ami                         = string,<br>    associate_public_ip_address = bool<br>    eip                         = bool<br>    root_block_device           = list(any)<br>    ebs_block_device            = list(any)<br>    cloud_init                  = string<br>    tags                        = map(string)<br>    ebs_optimized               = bool<br>  })</pre> | n/a | yes |
| <a name="input_elk_storage_alert_enabled"></a> [elk\_storage\_alert\_enabled](#input\_elk\_storage\_alert\_enabled) | Enable or disable the alarm for the ELK storage resize | `bool` | `false` | no |
| <a name="input_instance_logs_retention_in_days"></a> [instance\_logs\_retention\_in\_days](#input\_instance\_logs\_retention\_in\_days) | The number of days you want to retain log events in the instance log group. | `number` | `0` | no |
| <a name="input_pr_ami_id"></a> [pr\_ami\_id](#input\_pr\_ami\_id) | AMI ID for the Pull Request validation. If not set, it will be taken from the SSM parameter store. | `string` | `""` | no |
| <a name="input_project_ENVIRONMENT"></a> [project\_ENVIRONMENT](#input\_project\_ENVIRONMENT) | Another target something to deploy to. Used: QA or PROD. project\_ENVIRONMENT | `string` | `"QA"` | no |
| <a name="input_rds"></a> [rds](#input\_rds) | n/a | `any` | `{}` | no |
| <a name="input_rds_kms_share_backup_account"></a> [rds\_kms\_share\_backup\_account](#input\_rds\_kms\_share\_backup\_account) | rds kms key need to share with central backup account for cross account backup, use Audit account for prd env and cloudops dev account for stg/dev env | `string` | `"arn:aws:iam::************:root"` | no |
| <a name="input_rds_master_pass_length"></a> [rds\_master\_pass\_length](#input\_rds\_master\_pass\_length) | length of the randonmly generated password for the rds master account. | `number` | `24` | no |
| <a name="input_rds_params"></a> [rds\_params](#input\_rds\_params) | n/a | `any` | <pre>{<br>  "cluster_group": [],<br>  "ordinary_group": []<br>}</pre> | no |
| <a name="input_rds_snapshot_identifier"></a> [rds\_snapshot\_identifier](#input\_rds\_snapshot\_identifier) | Specifies whether or not to create this cluster from a snapshot. You can use either the name or ARN when specifying a DB cluster snapshot, or the ARN when specifying a DB snapshot | `string` | `null` | no |
| <a name="input_required_tags"></a> [required\_tags](#input\_required\_tags) | n/a | `map(string)` | `{}` | no |
| <a name="input_s3_ssl_bucket_name"></a> [s3\_ssl\_bucket\_name](#input\_s3\_ssl\_bucket\_name) | Manual s3 bucket name for ssl certificates to be pulled from. | `string` | `""` | no |
| <a name="input_s3_ssl_file_cert"></a> [s3\_ssl\_file\_cert](#input\_s3\_ssl\_file\_cert) | Manual s3 cert file location. | `string` | `""` | no |
| <a name="input_s3_ssl_file_key"></a> [s3\_ssl\_file\_key](#input\_s3\_ssl\_file\_key) | Manual s3 cert file location. | `string` | `""` | no |
| <a name="input_secrets_manager_recovery_window_days"></a> [secrets\_manager\_recovery\_window\_days](#input\_secrets\_manager\_recovery\_window\_days) | Secret manager recovery window in days. Can be set to 0 for allowing immediate deletes. | `number` | `30` | no |
| <a name="input_security_groups"></a> [security\_groups](#input\_security\_groups) | n/a | `any` | `{}` | no |
| <a name="input_strong_dm_enabled"></a> [strong\_dm\_enabled](#input\_strong\_dm\_enabled) | n/a | `bool` | `true` | no |
| <a name="input_subnet_private"></a> [subnet\_private](#input\_subnet\_private) | Warning: [Fixable] variable "subnet\_db" is declared but not used (terraform\_unused\_declarations) variable "subnet\_db" { type = string } | `string` | n/a | yes |
| <a name="input_subnet_public"></a> [subnet\_public](#input\_subnet\_public) | n/a | `string` | n/a | yes |
| <a name="input_util"></a> [util](#input\_util) | n/a | <pre>object({<br>    name                        = string,<br>    instance_type               = string,<br>    count                       = number,<br>    ami                         = string,<br>    associate_public_ip_address = bool<br>    eip                         = bool<br>    root_block_device           = list(any)<br>    ebs_block_device            = list(any)<br>    cloud_init                  = string<br>    tags                        = map(string)<br>  })</pre> | n/a | yes |
| <a name="input_vpc_name"></a> [vpc\_name](#input\_vpc\_name) | n/a | `string` | n/a | yes |
| <a name="input_web"></a> [web](#input\_web) | n/a | <pre>object({<br>    name                        = string,<br>    instance_type               = string,<br>    count                       = number,<br>    ami                         = string,<br>    associate_public_ip_address = bool<br>    eip                         = bool<br>    root_block_device           = list(any)<br>    ebs_block_device            = list(any)<br>    cloud_init                  = string<br>    tags                        = map(string)<br>    ebs_optimized               = bool<br>  })</pre> | n/a | yes |
| <a name="input_web_storage_alert_enabled"></a> [web\_storage\_alert\_enabled](#input\_web\_storage\_alert\_enabled) | Enable or disable the alarm for the WEB storage resize | `bool` | `false` | no |
| <a name="input_web_target_groups"></a> [web\_target\_groups](#input\_web\_target\_groups) | n/a | `list(map(string))` | `[]` | no |
| <a name="input_zabbix"></a> [zabbix](#input\_zabbix) | n/a | <pre>object({<br>    name                        = string,<br>    instance_type               = string,<br>    count                       = number,<br>    ami                         = string,<br>    associate_public_ip_address = bool<br>    eip                         = bool<br>    root_block_device           = list(any)<br>    ebs_block_device            = list(any)<br>    cloud_init                  = string<br>    tags                        = map(string)<br>    ebs_optimized               = bool<br>  })</pre> | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_cluster_resource_id"></a> [cluster\_resource\_id](#output\_cluster\_resource\_id) | The RDS Cluster Resource ID |
| <a name="output_ebs_block_device"></a> [ebs\_block\_device](#output\_ebs\_block\_device) | n/a |
| <a name="output_instance_ids"></a> [instance\_ids](#output\_instance\_ids) | n/a |
| <a name="output_instances"></a> [instances](#output\_instances) | n/a |
| <a name="output_rds_cluster_id"></a> [rds\_cluster\_id](#output\_rds\_cluster\_id) | RDS cluster identifier |
<!-- END_TF_DOCS -->