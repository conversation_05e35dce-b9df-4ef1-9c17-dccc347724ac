# Setup infra and access to perform backup and migration tasks

The module create required s3 bucket and add permittions to instance role to run backup/restore procedures as desribed here: 
[Centrum upgrade procedure](https://pepperstone.atlassian.net/wiki/spaces/Trading/pages/3067805767/Centrum+upgrade+procedure)

This module sets up an S3 bucket for backups and migrations.
/It uses a module called "s3_backup_migration" to create the S3 bucket.
The module takes in the following parameters:
 - s3_bucket_name: The name of the S3 bucket for backups and migrations.
 - logging_bucket: The name of the S3 bucket for access logs.
 - logging_prefix: The prefix for the access logs in the logging bucket.
 - lifecycle_rules: A list of lifecycle rules for the S3 bucket.
 - tags: A map of tags to apply to the S3 bucket.

It also creates an IAM policy document called "s3_backup_migration_rw_access"
that allows read-write access to the S3 bucket. It includes two statements:
 - AccessToS3Bucket: Allows access to the S3 bucket.
 - AccessToS3BucketObjects: Allows access to objects within the S3 bucket.

And attaches the IAM policy document to IAM roles specified in the "roles" variable.
