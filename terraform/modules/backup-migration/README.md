<!-- BEGIN_TF_DOCS -->
# Setup infra and access to perform backup and migration tasks

The module create required s3 bucket and add permittions to instance role to run backup/restore procedures as desribed here:
[Centrum upgrade procedure](https://pepperstone.atlassian.net/wiki/spaces/Trading/pages/3067805767/Centrum+upgrade+procedure)

This module sets up an S3 bucket for backups and migrations.
/It uses a module called "s3\_backup\_migration" to create the S3 bucket.
The module takes in the following parameters:
 - s3\_bucket\_name: The name of the S3 bucket for backups and migrations.
 - logging\_bucket: The name of the S3 bucket for access logs.
 - logging\_prefix: The prefix for the access logs in the logging bucket.
 - lifecycle\_rules: A list of lifecycle rules for the S3 bucket.
 - tags: A map of tags to apply to the S3 bucket.

It also creates an IAM policy document called "s3\_backup\_migration\_rw\_access"
that allows read-write access to the S3 bucket. It includes two statements:
 - AccessToS3Bucket: Allows access to the S3 bucket.
 - AccessToS3BucketObjects: Allows access to objects within the S3 bucket.

And attaches the IAM policy document to IAM roles specified in the "roles" variable.

## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.3.0, < 1.6.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_s3_backup_migration"></a> [s3\_backup\_migration](#module\_s3\_backup\_migration) | git::https://github.com/pepperstone/terraform-dxtrade-ci//modules/s3 | 8f14547a91822f70a753855aa5ac915347eb7860 |

## Resources

| Name | Type |
|------|------|
| [aws_iam_policy.migration_secrets_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.s3_backup_migration_rw_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_role_policy_attachment.migration_secrets_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.s3_backup_migration_rw_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_iam_policy_document.migration_secrets_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.s3_backup_migration_rw_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_environment"></a> [environment](#input\_environment) | The name of the environment in which the systemis being deployed, select from dev, stg, or prd | `string` | n/a | yes |
| <a name="input_roles"></a> [roles](#input\_roles) | The list of roles to be attached to the instance profile | `list(string)` | n/a | yes |

## Outputs

No outputs.
<!-- END_TF_DOCS -->