variable "environment" {
  type        = string
  description = "The name of the environment in which the systemis being deployed, select from dev, stg, or prd"

  validation {
    condition     = contains(["dev", "stg", "prd"], var.environment)
    error_message = "The environment must be \"dev\", \"stg\" or \"prd\"."
  }
}


variable "roles" {
  type        = list(string)
  description = "The list of roles to be attached to the instance profile"
}