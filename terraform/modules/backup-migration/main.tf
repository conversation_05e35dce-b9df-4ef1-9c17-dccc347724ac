module "s3_backup_migration" {
  source         = "git::https://github.com/pepperstone/terraform-dxtrade-ci//modules/s3?ref=8f14547a91822f70a753855aa5ac915347eb7860"
  s3_bucket_name = "pepperstone-centrum-backup-${var.environment}"

  logging_bucket = "s3-access-log-${data.aws_caller_identity.current.account_id}-${data.aws_region.current.name}"
  logging_prefix = "pepperstone-centrum-backup-${var.environment}/"

  lifecycle_rules = [
    {
      id     = "current"
      status = "Enabled"

      expiration = {
        days = 60
      }
    }
  ]

  tags = {
    "DataCriticality" = "low"
    "DataSensitivity" = "low"
  }

}

data "aws_iam_policy_document" "s3_backup_migration_rw_access" {
  statement {
    sid    = "AccessToS3Bucket"
    effect = "Allow"
    resources = [
      module.s3_backup_migration.s3_bucket_arn
    ]

    actions = [
      "s3:*"
    ]
  }

  statement {
    sid    = "AccessToS3BucketObjects"
    effect = "Allow"
    resources = [
      "${module.s3_backup_migration.s3_bucket_arn}/*"
    ]

    actions = [
      "s3:*"
    ]
  }
}

resource "aws_iam_policy" "s3_backup_migration_rw_access" {
  description = "Access to S3 bucket for backup migration"
  name        = "s3_backup_migration_rw_access"
  path        = "/"
  policy      = data.aws_iam_policy_document.s3_backup_migration_rw_access.json
}


resource "aws_iam_role_policy_attachment" "s3_backup_migration_rw_access" {
  for_each = toset(var.roles)

  policy_arn = aws_iam_policy.s3_backup_migration_rw_access.arn
  role       = each.value
}


data "aws_iam_policy_document" "migration_secrets_access" {
  statement {
    sid    = "AccessToSecrets"
    effect = "Allow"
    resources = [
      "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:*"
    ]

    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:ListSecrets",
      "secretsmanager:ListSecretVersionIds",
    ]
  }
}

resource "aws_iam_policy" "migration_secrets_access" {
  description = "Access to secrets for backups and migration"
  name        = "migration_secrets_access"
  path        = "/"
  policy      = data.aws_iam_policy_document.migration_secrets_access.json
}

resource "aws_iam_role_policy_attachment" "migration_secrets_access" {
  for_each = toset(var.roles)

  policy_arn = aws_iam_policy.migration_secrets_access.arn
  role       = each.value
}

data "aws_iam_policy_document" "parameters_access" {
  statement {
    sid    = "AccessToParameters"
    effect = "Allow"
    resources = [
      "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/*"
    ]

    actions = [
      "ssm:GetParameter",
      "ssm:GetParameters",
      "ssm:GetParametersByPath",
    ]
  }

}

resource "aws_iam_policy" "parameters_access" {
  description = "Access to SSM parameters for backups and migration"
  name        = "parameters_access"
  path        = "/"
  policy      = data.aws_iam_policy_document.parameters_access.json
}

resource "aws_iam_role_policy_attachment" "parameters_access" {
  for_each = toset(var.roles)

  policy_arn = aws_iam_policy.parameters_access.arn
  role       = each.value
}