# This Terraform module creates resources for document storage resize automation.

# Creates an AWS Systems Manager (SSM) document.
resource "aws_ssm_document" "this" {
  name            = var.ssm_document_name
  document_type   = "Automation"
  document_format = "YAML"
  target_type     = "/AWS::EC2::Instance"
  content         = file("${path.module}/ssm_document.yml")
}

# Creates an IAM role for the SSM document.
resource "aws_iam_role" "ssm_document" {
  name        = "${var.ssm_document_name}_automation_runbook_exec"
  description = "Allows SSM to exec automation runbook on your behalf"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Sid    = "AllowSSMAssumeRole"
        Principal = {
          Service = [
            "ssm.amazonaws.com",
            "events.amazonaws.com"
          ]
        }
      }
    ]
  })
}

# Creates an IAM policy for the SSM document.
resource "aws_iam_policy" "ssm_document" {
  name        = "${var.ssm_document_name}_automation_runbook_exec_policy"
  path        = "/"
  description = "IAM policy to be used when the SSM document is executed"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:SendCommand",
          "ssm:DescribeInstanceInformation",
          "ssm:GetCommandInvocation",
          "ec2:CreateSnapshot",
          "ec2:DeleteSnapshot",
          "ec2:ModifyVolume",
          "ec2:CreateTags",
          "ec2:DescribeVolumes",
          "ec2:DescribeSnapshots",
          "ec2:DescribeInstances",
          "ec2:DescribeVolumesModifications",
        ]
        Resource = "*"
      }
    ]
  })
}

# Attaches the IAM policy to the IAM role for the SSM document.
resource "aws_iam_role_policy_attachment" "ssm_document" {
  role       = aws_iam_role.ssm_document.name
  policy_arn = aws_iam_policy.ssm_document.arn
}

# Creates an IAM role for events.
resource "aws_iam_role" "events" {
  name        = "${var.ssm_document_name}_events_role"
  description = "IAM role to be used when the event rule is triggered"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Sid    = ""
        Principal = {
          Service = [
            "events.amazonaws.com"
          ]
        }
      }
    ]
  })
}

# Attaches the IAM policy to the events IAM role.
resource "aws_iam_role_policy_attachment" "events" {
  role       = aws_iam_role.events.name
  policy_arn = aws_iam_policy.events.arn
}

# Creates an IAM policy document for the events IAM role to allow the SSM document to be executed.
resource "aws_iam_policy" "events" {
  name        = "${var.ssm_document_name}_events_policy"
  description = "IAM policy to be used when the event rule is triggered"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "AllowSSMExec"
        Action = [
          "ssm:StartAutomationExecution"
        ]
        Effect   = "Allow"
        Resource = ["*"]
      },
      {
        Sid = "AllowPassRole"
        Action = [
          "iam:PassRole"
        ]
        Effect = "Allow"
        Resource = [
          aws_iam_role.ssm_document.arn
        ]
      }
    ]
  })
}

# Creates an SNS topic.
resource "aws_sns_topic" "this" {
  name = "${var.ssm_document_name}-events-notification-topic"
}

# Creates an SNS topic policy.
resource "aws_sns_topic_policy" "this" {
  arn    = aws_sns_topic.this.arn
  policy = data.aws_iam_policy_document.this.json
}

# Retrieves the AWS account ID.
data "aws_iam_policy_document" "this" {
  statement {
    sid = "ssm-automation-runbook-exec"
    principals {
      type        = "AWS"
      identifiers = [data.aws_caller_identity.this.account_id]
    }
    effect = "Allow"
    actions = [
      "SNS:GetTopicAttributes",
      "SNS:SetTopicAttributes",
      "SNS:AddPermission",
      "SNS:RemovePermission",
      "SNS:DeleteTopic",
      "SNS:Subscribe",
      "SNS:ListSubscriptionsByTopic"
    ]
    resources = [aws_sns_topic.this.arn]
  }

  statement {
    sid = "SNS publish"
    principals {
      type        = "Service"
      identifiers = ["cloudwatch.amazonaws.com"]
    }
    effect = "Allow"
    actions = [
      "sns:Publish"
    ]
    resources = [aws_sns_topic.this.arn]
    condition {
      test     = "ArnLike"
      variable = "aws:SourceArn"
      values   = ["arn:aws:cloudwatch:*:${data.aws_caller_identity.this.account_id}:alarm:*"]
    }
  }
}

# Creates an SNS topic subscription for email notifications.
resource "aws_sns_topic_subscription" "topic_email_subscription" {
  topic_arn = aws_sns_topic.this.arn
  protocol  = "email"
  endpoint  = var.email_notifications_endpoint
}
