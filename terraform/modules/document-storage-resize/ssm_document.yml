schemaVersion: '0.3'
description: |-
  ## Name
    PST-ExtendEbsVolume.
  ## Intent
    Extends EBS Volume on instances with nvme ebs volumes
    Details here: https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instance-store-volumes.html
  ## Supported operating systems: 
    * Linux
    * Tested amazon linux and ubuntu instances.
  ## Supported file systems: 
    * xfs 
    * ext4
  ## Notes
    * Document was created on base of the following document: AWS-ExtendEbsVolume.
    * A role with enough permissions should be provided for the document to be able to start or stop the configuration recording (otherwise the document will run with the caller identity).
    * Targeted instances must be managed by System Manager.
    * The operating systems types supported is Linux
    * The MountPoint is required for linux.
    * A snapshot from the EBS volume is created before extending it.
    * File systems supported are xfs and Ext4.
  ## Minimum permissions required:
    * ssm:SendCommand.
    * ssm:DescribeInstanceInformation.
    * ssm:GetCommandInvocation.
    * ec2:CreateSnapshot.
    * ec2:DeleteSnapshot.
    * ec2:ModifyVolume.
    * ec2:CreateTags.
    * ec2:DescribeVolumes.
assumeRole: '{{ AutomationAssumeRole }}'
parameters:
  VolumeId:
    type: String
    description: (Required) The volume to be extended.
    allowedPattern: ^vol-([a-z0-9\-]+)$
  MountPoint:
    type: String
    description: (Optional) The mount point (such as "/", "/data", ...) of the partition which is to be increased(Required for Linux).
    default: ''
    allowedPattern: ^$|^((\/\w+)+|\/?)$
  keepSnapShot:
    type: Boolean
    description: (Optional) A boolean flag to determine whether to remove the created snapshot after successful resizing of the volume and the file system(True by default).
    default: true
    allowedValues:
      - 'true'
      - 'false'
  InstanceId:
    type: String
    description: (Optional) The identifier of the instance requiring increase of volume.
    default: ''
    allowedPattern: ^$|i-[a-z0-9]{8,17}$
  AutomationAssumeRole:
    type: AWS::IAM::Role::Arn
    description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf
    default: ''
    allowedPattern: ^$|arn:(aws[a-zA-Z-]*)?:iam::\d{12}:role\/?[a-zA-Z_0-9+=,.@\-_\/]+
mainSteps:
  - description: |
      Extends EBS Volume
    name: ExtendEbsVolume
    action: aws:executeScript
    timeoutSeconds: 300
    isCritical: true
    isEnd: true
    onFailure: Abort
    inputs:
      Runtime: python3.8
      Handler: extend_ebs
      InputPayload:
        volume_id: '{{VolumeId}}'
        instance_id: '{{InstanceId}}'
        keep_snapshot: '{{keepSnapShot}}'
        mount_point: '{{MountPoint}}'
        current_execution_id: '{{automation:EXECUTION_ID}}'
      Script: |
        import time

        import boto3

        ec2_client = boto3.client("ec2")
        ssm_client = boto3.client("ssm")

        def extend_ebs(event, context):
            platform_type = None
            if event["instance_id"] != "":
                target_size = get_target_size(event["instance_id"], event["volume_id"])
                
            if event["instance_id"] != "":
                platform_type = get_operating_system_by_instance_id(
                    event["instance_id"]
                )

            if platform_type != "Linux":
                raise Exception("Unsupported Platform")

            snapshot_id = create_snapshot(
                event["volume_id"], event["current_execution_id"]
            )

            extend_ebs_volume(event["volume_id"], int(target_size))

            if not event["keep_snapshot"]:
                delete_snapshot(snapshot_id)

            if event["instance_id"] != "":
                if platform_type == "Linux":
                    return extend_file_system_for_linux(
                        event["instance_id"], event["mount_point"]
                    )
                else:
                    raise Exception("Unsupported Platform")
            return "volume extended successfully"


        def get_target_size(instance_id, volume_id):
            volumes = ec2_client.describe_volumes(VolumeIds=[volume_id])
            volume = volumes['Volumes'][0]
            return volume['Size']*1.2


        def extend_ebs_volume(volume_id, target_size):
            ec2_client.modify_volume(
                VolumeId=volume_id,
                Size=target_size,
            )
            while True:
                response = ec2_client.describe_volumes(VolumeIds=[volume_id])
                if response["Volumes"][0]["Size"] == target_size:
                    return "success"
                time.sleep(3)


        def get_operating_system_by_instance_id(instance_id):
            try:
                os_type = ssm_client.describe_instance_information(
                    InstanceInformationFilterList=[
                        {"key": "InstanceIds", "valueSet": [instance_id]}
                    ]
                )
                if len(os_type["InstanceInformationList"]) > 0:
                    return os_type["InstanceInformationList"][0]["PlatformType"]
                else:
                    raise Exception("The instance must be managed by system manager")
            except Exception as e:
                raise Exception(e)


        def extend_file_system_for_linux(instance_id, mount_point):
            response = ssm_client.send_command(
                InstanceIds=[instance_id],
                DocumentName="AWS-RunShellScript",
                TimeoutSeconds=500,
                Parameters={
                    "commands": [
                        "#!/bin/bash",
                        "set -x",
                        "findmnt -T {} || exit 1".format(
                            mount_point
                        ),  # make sure that the mount point is valid
                        "partition=`findmnt -T {} | awk '{{print $2}}' | sed -n '2 p'`".format(
                            mount_point
                        ),
                        "deviceName=`lsblk -npo pkname $partition`",
                        "partitionNumber=1",
                        "sudo growpart $deviceName $partitionNumber",
                        "sudo xfs_growfs -d {} || sudo resize2fs $partition".format(
                            mount_point
                        ),
                    ]
                },
            )
            command_id = response["Command"]["CommandId"]
            status, status_details = get_command_status_with_wait(
                instance_id, command_id
            )
            if status_details == "Failed":
                raise Exception("Error extending the file system")
            return "volume extended successfully"


        def create_snapshot(volume_id, exec_id):
            try:
                response = ec2_client.create_snapshot(
                    Description="a snapshot before the volume resizing",
                    VolumeId=volume_id,
                    TagSpecifications=[
                        {
                            "ResourceType": "snapshot",
                            "Tags": [{"Key": "execution_id", "Value": exec_id}],
                        },
                    ],
                )
                return response["SnapshotId"]
            except Exception as e:
                raise Exception(e)


        def delete_snapshot(snapshot_id):
            try:
                ec2_client.delete_snapshot(
                    SnapshotId=snapshot_id,
                )
            except Exception as e:
                raise Exception(e)


        def get_command_status_with_wait(instance_id, command_id):
            time.sleep(10)
            response = ssm_client.get_command_invocation(
                CommandId=command_id, InstanceId=instance_id
            )
            status = response["Status"]
            details = response["StatusDetails"]
            return status, details
    outputs:
      - Name: output
        Selector: $.Payload.output
        Type: String
