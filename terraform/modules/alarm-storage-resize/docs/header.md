# Storage resize trigger alarm

This Terraform module defines resources for monitoring and resizing the storage partition of an EC2 instance.

Resource: aws_cloudwatch_metric_alarm.this
Description: This resource creates a CloudWatch metric alarm to monitor the size of the partition of the EC2 instance.
Parameters:
  - alarm_name: The name of the alarm.
  - alarm_description: The description of the alarm.
  - comparison_operator: The operator used to compare the metric value with the threshold.
  - evaluation_periods: The number of periods over which data is compared to the threshold.
  - datapoints_to_alarm: The number of datapoints that must be breaching to trigger the alarm.
  - dimensions: The dimensions to filter the metric data.
  - metric_name: The name of the metric to monitor.
  - namespace: The namespace of the metric.
  - period: The period in seconds over which the specified statistic is applied.
  - statistic: The statistic to apply to the alarm's associated metric.
  - threshold: The value against which the specified statistic is compared.
  - alarm_actions: The actions to execute when the alarm state changes.

Resource: aws_cloudwatch_event_rule.this
Description: This resource creates a CloudWatch event rule that triggers when the partition is more than a specified threshold full.
Parameters:
  - name: The name of the event rule.
  - description: The description of the event rule.
  - event_pattern: The event pattern to filter events.

Resource: aws_cloudwatch_event_target.this
Description: This resource creates a CloudWatch event target that specifies the target for the event rule.
Parameters:
  - rule: The name of the event rule to associate with the target.
  - target_id: The unique target ID.
  - arn: The Amazon Resource Name (ARN) of the target.
  - role_arn: The Amazon Resource Name (ARN) of the IAM role to use for this target.
  - input: Valid JSON text passed to the target.