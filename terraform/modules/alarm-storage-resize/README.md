<!-- BEGIN_TF_DOCS -->
# Storage resize trigger alarm

This Terraform module defines resources for monitoring and resizing the storage partition of an EC2 instance.

Resource: aws\_cloudwatch\_metric\_alarm.this
Description: This resource creates a CloudWatch metric alarm to monitor the size of the partition of the EC2 instance.
Parameters:
  - alarm\_name: The name of the alarm.
  - alarm\_description: The description of the alarm.
  - comparison\_operator: The operator used to compare the metric value with the threshold.
  - evaluation\_periods: The number of periods over which data is compared to the threshold.
  - datapoints\_to\_alarm: The number of datapoints that must be breaching to trigger the alarm.
  - dimensions: The dimensions to filter the metric data.
  - metric\_name: The name of the metric to monitor.
  - namespace: The namespace of the metric.
  - period: The period in seconds over which the specified statistic is applied.
  - statistic: The statistic to apply to the alarm's associated metric.
  - threshold: The value against which the specified statistic is compared.
  - alarm\_actions: The actions to execute when the alarm state changes.

Resource: aws\_cloudwatch\_event\_rule.this
Description: This resource creates a CloudWatch event rule that triggers when the partition is more than a specified threshold full.
Parameters:
  - name: The name of the event rule.
  - description: The description of the event rule.
  - event\_pattern: The event pattern to filter events.

Resource: aws\_cloudwatch\_event\_target.this
Description: This resource creates a CloudWatch event target that specifies the target for the event rule.
Parameters:
  - rule: The name of the event rule to associate with the target.
  - target\_id: The unique target ID.
  - arn: The Amazon Resource Name (ARN) of the target.
  - role\_arn: The Amazon Resource Name (ARN) of the IAM role to use for this target.
  - input: Valid JSON text passed to the target.

## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0, < 1.6.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_cloudwatch_event_rule.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_event_rule) | resource |
| [aws_cloudwatch_event_target.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_event_target) | resource |
| [aws_cloudwatch_metric_alarm.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_metric_alarm) | resource |
| [aws_caller_identity.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_region.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_alarm_actions"></a> [alarm\_actions](#input\_alarm\_actions) | The list of actions to take when the alarm is triggered | `list(string)` | `[]` | no |
| <a name="input_aws_ssm_document_name"></a> [aws\_ssm\_document\_name](#input\_aws\_ssm\_document\_name) | The name of the SSM document to execute | `string` | `"AWS-RunShellScript"` | no |
| <a name="input_events_iam_role_arm"></a> [events\_iam\_role\_arm](#input\_events\_iam\_role\_arm) | The ARN of the IAM role to use for the CloudWatch event target | `string` | `""` | no |
| <a name="input_instance"></a> [instance](#input\_instance) | The map of EC2 instances and EBS volumes to extend | <pre>object({<br>    id            = string<br>    ami           = string<br>    instance_type = string<br>    volume_id     = string<br>    mount_point   = string<br>    keep_snapshot = bool<br>    device        = string<br>    fstype        = string<br>  })</pre> | <pre>{<br>  "ami": "ami-01a2b3c4d5e6f7g8h",<br>  "device": "nvme1n1p1",<br>  "fstype": "xfs",<br>  "id": "i-01a2b3c4d5e6f7g8h",<br>  "instance_type": "t2.micro",<br>  "keep_snapshot": false,<br>  "mount_point": "/opt",<br>  "volume_id": "vol-01a2b3c4d5e6f7g8h"<br>}</pre> | no |
| <a name="input_ssm_document_iam_role"></a> [ssm\_document\_iam\_role](#input\_ssm\_document\_iam\_role) | The ARN of the IAM role to use for the SSM document | `string` | `""` | no |
| <a name="input_target_id"></a> [target\_id](#input\_target\_id) | The unique target assignment ID. If missing, will generate a random, unique id. | `string` | `"extend_storage"` | no |
| <a name="input_threshold"></a> [threshold](#input\_threshold) | The threshold for the alarm | `number` | `90` | no |

## Outputs

No outputs.
<!-- END_TF_DOCS -->