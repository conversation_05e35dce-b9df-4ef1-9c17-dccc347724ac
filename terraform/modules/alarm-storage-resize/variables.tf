# Variables
variable "instance" {
  description = "The map of EC2 instances and EBS volumes to extend"
  type = object({
    id            = string
    ami           = string
    instance_type = string
    volume_id     = string
    mount_point   = string
    keep_snapshot = bool
    device        = string
    fstype        = string
  })
  default = {
    "id"            = "i-01a2b3c4d5e6f7g8h"
    "ami"           = "ami-01a2b3c4d5e6f7g8h"
    "instance_type" = "t2.micro"
    "volume_id"     = "vol-01a2b3c4d5e6f7g8h"
    "mount_point"   = "/opt"
    "keep_snapshot" = false
    "device"        = "nvme1n1p1"
    "fstype"        = "xfs"
  }
}

variable "threshold" {
  description = "The threshold for the alarm"
  type        = number
  default     = 90
}

variable "target_id" {
  description = "The unique target assignment ID. If missing, will generate a random, unique id."
  type        = string
  default     = "extend_storage"
}

variable "alarm_actions" {
  description = "The list of actions to take when the alarm is triggered"
  type        = list(string)
  default     = []
}

variable "aws_ssm_document_name" {
  description = "The name of the SSM document to execute"
  type        = string
  default     = "AWS-RunShellScript"
}

variable "events_iam_role_arm" {
  description = "The ARN of the IAM role to use for the CloudWatch event target"
  type        = string
  default     = ""
}

variable "ssm_document_iam_role" {
  description = "The ARN of the IAM role to use for the SSM document"
  type        = string
  default     = ""
}