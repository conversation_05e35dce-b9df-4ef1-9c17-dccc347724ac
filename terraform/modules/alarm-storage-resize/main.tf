resource "aws_cloudwatch_metric_alarm" "this" {
  alarm_name          = "partition_size_alarm_${var.instance.id}"
  alarm_description   = "This metric monitors the size of the partition of EC2 instance ${var.instance.id}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  datapoints_to_alarm = "2"
  dimensions = {
    InstanceId   = var.instance.id
    path         = var.instance.mount_point
    device       = var.instance.device
    fstype       = var.instance.fstype
    ImageId      = var.instance.ami
    InstanceType = var.instance.instance_type
  }
  metric_name   = "disk_used_percent"
  namespace     = "CWAgent"
  period        = "300"
  statistic     = "Average"
  threshold     = var.threshold
  alarm_actions = var.alarm_actions
}

resource "aws_cloudwatch_event_rule" "this" {
  name        = "ExtendPartitionRule-${var.instance.id}"
  description = "This rule triggers when the ${var.instance.mount_point} partition is more than ${var.threshold}% full"
  event_pattern = jsonencode(
    {
      detail = {
        alarmName = [
          aws_cloudwatch_metric_alarm.this.alarm_name,
        ]
        previousState = {
          value = [
            "OK",
          ]
        }
        state = {
          value = [
            "ALARM",
          ]
        }
      }
      detail-type = [
        "CloudWatch Alarm State Change",
      ]
      source = [
        "aws.cloudwatch",
      ]
    }
  )
}

resource "aws_cloudwatch_event_target" "this" {
  rule      = aws_cloudwatch_event_rule.this.name
  target_id = "${var.target_id}-${var.instance.id}"
  arn       = "arn:aws:ssm:${data.aws_region.this.name}:${data.aws_caller_identity.this.account_id}:automation-definition/${var.aws_ssm_document_name}"
  role_arn  = var.events_iam_role_arm
  input = jsonencode(
    {
      AutomationAssumeRole = [
        var.ssm_document_iam_role,
      ]
      InstanceId = [
        var.instance.id,
      ]
      MountPoint = [
        var.instance.mount_point,
      ]
      VolumeId = [
        var.instance.volume_id,
      ]
      keepSnapShot = [
        var.instance.keep_snapshot,
      ]
    }
  )
}
