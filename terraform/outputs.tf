output "instances" {
  value = {
    "app_ip_address"  = module.app[*].private_ip
    "web_ip_address"  = module.web[*].private_ip
    "util_ip_address" = module.util[*].private_ip
    "RDS" = {
      "endpoint" = module.db.cluster_endpoint
      "username" = module.db.cluster_master_username,
    }
    "awsLB_ext" = aws_lb.main_lb_ext.dns_name
    "awsLB_int" = aws_lb.main_lb_int.dns_name
    "LB_ext"    = aws_route53_record.lbext.fqdn
    "LB_int"    = aws_route53_record.lbint.fqdn
  }
}

output "instance_ids" {
  value = {
    "app_instance_ids"    = module.app[*].id
    "web_instance_ids"    = module.web[*].id
    "util_instance_ids"   = module.util[*].id
    "elk_instance_ids"    = module.elk[*].id
    "zabbix_instance_ids" = module.zabbix[*].id
  }
}

output "ebs_block_device" {
  value = {
    "app_ebs_block_device"    = module.app[*].ebs_block_device
    "web_ebs_block_device"    = module.web[*].ebs_block_device
    "util_ebs_block_device"   = module.util[*].ebs_block_device
    "elk_ebs_block_device"    = module.elk[*].ebs_block_device
    "zabbix_ebs_block_device" = module.zabbix[*].ebs_block_device
  }
}

output "rds_cluster_id" {
  value       = module.db.cluster_id
  description = "RDS cluster identifier"
}
output "cluster_resource_id" {
  value       = module.db.cluster_resource_id
  description = "The RDS Cluster Resource ID"
}