name: 'Terraform Module Release'

on:
  push:
    branches:
    - main

jobs:
  release:
    name: 'Create Release Tag'
    runs-on: ubuntu-latest

    # Use the Bash shell regardless whether the GitHub Actions runner is ubuntu-latest, macos-latest, or windows-latest
    defaults:
      run:
        shell: bash

    steps:
    - name: Setup Node.js
      id: setup-node
      uses: actions/setup-node@60edb5dd545a775178f52524783378180af0d1f8 # v4.0.2
      with:
        node-version: 20

    - name: Checkout
      uses: actions/checkout@44c2b7a8a4ea60a981eaca3cf939b5f4305c123b # v4.1.5
      with:
        fetch-depth: 0

    - name: Init Terraform  
      uses: docker://hashicorp/terraform:1.5.6
      with:
        entrypoint: terraform
        args: -chdir=./terraform init

    - name: Validate Terraform    
      uses: docker://hashicorp/terraform:1.5.6
      with:
        entrypoint: terraform
        args: -chdir=./terraform validate

    - name: Check Terraform formatting    
      uses: docker://hashicorp/terraform:1.5.6
      with:
        entrypoint: terraform
        args: fmt -check -recursive

    - name: Bump and Tag Version
      id: tag
      uses: jefflinse/pr-semver-bump@06f7c64423a32373c86ba050286825832166ddd5 # v1.6.2
      with:
        mode: bump
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        major-label: major
        minor-label: minor
        patch-label: patch
        with-v: true

    - name: Release
      uses: softprops/action-gh-release@9d7c94cfd0a1f3ed45544c887983e9fa900f0564 # v2.0.4
      with:
        body: ${{ steps.tag.outputs.release-notes }}
        token: ${{ secrets.GITHUB_TOKEN }}
        tag_name: ${{ steps.tag.outputs.version }}
        generate_release_notes: true
