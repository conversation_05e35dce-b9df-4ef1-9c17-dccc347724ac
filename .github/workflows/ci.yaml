name: 'Terraform Module Test'

on:
  pull_request:

jobs:
  validate_module:
    name: 'Execute Tests'
    runs-on: dxtrade-dev-us

    # Use the Bash shell regardless whether the GitHub Actions runner is ubuntu-latest, macos-latest, or windows-latest
    defaults:
      run:
        shell: bash

    steps:
    - name: Setup Node.js
      id: setup-node
      uses: actions/setup-node@60edb5dd545a775178f52524783378180af0d1f8 # v4.0.2
      with:
        node-version: 20

    - name: Checkout
      uses: actions/checkout@44c2b7a8a4ea60a981eaca3cf939b5f4305c123b # v4.1.5
      with:
        ref: ${{ github.event.pull_request.head.ref }}
    
    - name: Render terraform docs inside the README.md and push changes back to PR branch
      uses: terraform-docs/gh-actions@7a62208a0090636af2df1b739da46d27fd90bdc6 # v1.1.0
      if: github.ref != 'refs/heads/main'
      with:
        working-dir: terraform/
        output-file: README.md
        output-method: inject
        git-push: "true"

    - name: Init Terraform  
      uses: docker://hashicorp/terraform:1.5.6
      with:
        entrypoint: terraform
        args: -chdir=./terraform init

    - name: Validate Terraform    
      uses: docker://hashicorp/terraform:1.5.6
      with:
        entrypoint: terraform
        args: -chdir=./terraform validate

    - name: Check Terraform formatting    
      uses: docker://hashicorp/terraform:1.5.6
      with:
        entrypoint: terraform
        args: fmt -check -recursive

    - name: Init Example
      uses: docker://hashicorp/terraform:1.5.6
      with:
        entrypoint: terraform
        args: -chdir=./examples/dev init

    # TF Linting start
    - uses: terraform-linters/setup-tflint@19a52fbac37dacb22a09518e4ef6ee234f2d4987 # v4.0.0
      name: Setup TFLint

    - name: Show version
      run: tflint --version
      if: github.ref != 'refs/heads/main'

    - name: Init TFLint
      run: tflint --init
      if: github.ref != 'refs/heads/main'

    - name: Run TFLint
      run: tflint -f compact
      if: github.ref != 'refs/heads/main'
    # TF Linting end

    - name: Execute Plan for validity
      uses: docker://hashicorp/terraform:1.5.6
      with:
        entrypoint: terraform
        args: -chdir=./examples/dev plan

  validate_pr:
    name: 'Validate PR'
    runs-on: self-hosted
    steps:
    - name: Bump and Tag Version
      uses: jefflinse/pr-semver-bump@06f7c64423a32373c86ba050286825832166ddd5 # v1.6.2
      with:
        mode: validate
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        major-label: major
        minor-label: minor
        patch-label: patch
        with-v: true
