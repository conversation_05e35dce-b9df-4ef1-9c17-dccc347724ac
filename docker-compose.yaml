version: '3.1'

services: 

  localstack:
    image: localstack/localstack-pro:latest
 #   image: localstack/localstack:latest
    environment: 
      - AWS_DEFAULT_REGION=us-east-1
      - LOCALSTACK_API_KEY=${LOCALSTACK_API_KEY- }
    ports: 
      - '4566-4583:4566-4583'
    volumes: 
      - "${TEMPDIR:-/tmp/localstack}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
    healthcheck:
      test: ["CMD", "bash", "-c", "awslocal ec2 describe-subnets"]
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - test

  test:
    build: .
    command: make test
    depends_on: 
      localstack:
        condition: service_healthy
    networks:
      - test

networks:
  test:
    name: test