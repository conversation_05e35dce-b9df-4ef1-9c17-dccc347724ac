# Not actively used, will be removed later.
profile = "pst"

required_tags = {
  Project   = "Pepperstone"
  Platform  = "stg-demo"
  Env       = "stg"
  ManagedBy = "terraform"
}

aws_external_domain = "stg.oms.pepperstone.com"
aws_local_domain    = "us.stg.internal.pepperstone.com"

# security setting for ingress, for outgoing rules you will need to extend the config to require them
# for more info refer to security module and it's documentation
security_groups = {
  ingress_cidr_blocks = []
  ingress_with_cidr_blocks = [
    # ICMP questionable, but use full - denied does not meet compliance standards changed to internal ranges only
    {
      from_port   = -1
      to_port     = -1
      protocol    = "icmp"
      description = "User-service ports (ipv4)"
      cidr_blocks = "**********/22"
    },
    #SSH access , questionable, but to be double sure we have fail2ban installation as a default in our playbooks
    {
      from_port   = 22
      to_port     = 22
      protocol    = "tcp"
      description = "User-service ports (ipv4)"
      cidr_blocks = "**********/22"
    },
    # VPC network - denied too permissive
    {
      from_port   = 0
      to_port     = 65535
      protocol    = "TCP"
      description = "VPC network"
      cidr_blocks = "**********/22"
    },
    {
      from_port   = 0
      to_port     = 65535
      protocol    = "UDP"
      description = "VPC network"
      cidr_blocks = "**********/22"
    },

    ####### CLIENT BLOCK
    {
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      description = "HTTP"
      cidr_blocks = "0.0.0.0/0"
    },
    {
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      description = "HTTPS"
      cidr_blocks = "0.0.0.0/0"
    },
    {
      from_port   = 7500
      to_port     = 7503
      protocol    = "TCP"
      description = "DXCORE API"
      cidr_blocks = "0.0.0.0/0"
    },
    {
      from_port   = 7800
      to_port     = 7800
      protocol    = "TCP"
      description = "QUOTE DATA"
      cidr_blocks = "0.0.0.0/0"
    }
  ]
  ingress_rules = []
  egress_rules = [
    "all-all"
  ]
}

# reserved for static external services that you want to incorporate via route53 in to local space using short names
records = [
]



ebs_devices = [
  {
    index       = 0          # index of the instances section below, yes you can create multiple drives with he same index
    device_name = "/dev/sdb" # mount point
    type        = "gp2"      # type of instance
    size        = 150        # size
  },
  {
    index       = 1
    device_name = "/dev/sdb"
    type        = "gp2"
    size        = 150
  }
]

app = {
  name                        = "stg-demo-core",
  instance_type               = "m5a.4xlarge",
  count                       = 3,
  ami                         = "ami-0eacec0bf55b70bc9",
  associate_public_ip_address = false
  eip                         = false
  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 50
    }
  ]
  cloud_init = "./cloud_init/cloud_init_client.yml"
  tags = {
    "allocation" = "app"
  }
}

web = {
  name                        = "stg-demo-web",
  instance_type               = "m5a.2xlarge",
  count                       = 3,
  ami                         = "ami-0eacec0bf55b70bc9", //Oracle Linux ami-0eacec0bf55b70bc9 - us-east-1
  associate_public_ip_address = false
  eip                         = false
  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 50
    }
  ]

  cloud_init = "./cloud_init/cloud_init_client.yml"
  tags = {
    "allocation" = "web"
  }
}

util = {
  name                        = "stg-demo-util",
  instance_type               = "t3.medium",
  count                       = 3,
  ami                         = "ami-0eacec0bf55b70bc9",
  associate_public_ip_address = false
  eip                         = false
  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 30
    }
  ]

  cloud_init = "./cloud_init/cloud_init_client_nomad.yml"

  tags = {
  }
}

# basic RDS config
rds = {
  name = "stg-demo-postgresql"
  # this should be "aurora-postgresql" , because, the engine in AWS is named this way
  engine                = "aurora-postgresql"
  engine_version        = "12.8"
  replica_count         = 1
  instance_type         = "db.r5.large"
  instance_type_replica = "db.r5.large"

  shared_preload_libraries = "pg_stat_statements,pg_hint_plan"
}

rds_params = {
  ordinary_group : [
    {
      name         = "max_locks_per_transaction"
      value        = "2048"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_pred_locks_per_transaction"
      value        = "8192"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_connections"
      value        = "512"
      apply_method = "pending-reboot"
    },
    {
      name         = "pg_stat_statements.track"
      value        = "all"
      apply_method = "immediate"
    },
    {
      name         = "track_activity_query_size"
      value        = "8192"
      apply_method = "pending-reboot"
    },
    {
      name         = "track_io_timing"
      value        = "1"
      apply_method = "immediate"
    },
    {
      name         = "pg_stat_statements.max"
      value        = "5000"
      apply_method = "pending-reboot"
    },
    {
      name         = "auto_explain.log_min_duration"
      value        = "-1"
      apply_method = "immediate"
    },
    {
      name         = "auto_explain.log_nested_statements"
      value        = "0"
      apply_method = "immediate"
    }


    # Minimum statement execution time, in milliseconds.
    # If you set it to 250ms then all statements that run 250ms or longer will be logged. Setting this to 0 logs all plans.
    #    {
    #        name  = "auto_explain.log_min_duration"
    #        value =  5000
    #        apply_method = "pending-reboot"
    #    },
    #    # Causes nested statements (statements executed inside a function) to be considered for logging. When it is off, only top-level query plans are logged
    #    {
    #        name  = "auto_explain.log_nested_statements"
    #        value = 1
    #        apply_method = "pending-reboot"
    #    }
  ],
  cluster_group : [
    {
      name         = "max_locks_per_transaction"
      value        = "2048"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_pred_locks_per_transaction"
      value        = "8192"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_connections"
      value        = "512"
      apply_method = "pending-reboot"
    },
    {
      name         = "track_activity_query_size"
      value        = "8192"
      apply_method = "pending-reboot"
    },
    {
      name         = "track_io_timing"
      value        = "1"
      apply_method = "immediate"
    },
    {
      name         = "pg_stat_statements.max"
      value        = "5000"
      apply_method = "pending-reboot"
    },
    {
      name         = "pg_stat_statements.track"
      value        = "all"
      apply_method = "immediate"
    },
    {
      name         = "auto_explain.log_min_duration"
      value        = "-1"
      apply_method = "immediate"
    },
    {
      name         = "auto_explain.log_nested_statements"
      value        = "0"
      apply_method = "immediate"
    },
    {
      name         = "synchronous_commit"
      value        = "on"
      apply_method = "immediate"
    },
    {
      name         = "rds.logical_replication"
      value        = "1"
      apply_method = "pending-reboot"
    }

    # Minimum statement execution time, in milliseconds.
    # If you set it to 250ms then all statements that run 250ms or longer will be logged. Setting this to 0 logs all plans.
    #    {
    #        name  = "auto_explain.log_min_duration"
    #        value =  5000
    #        apply_method = "pending-reboot"
    #    },
    #    # Causes nested statements (statements executed inside a function) to be considered for logging. When it is off, only top-level query plans are logged
    #    {
    #        name  = "auto_explain.log_nested_statements"
    #        value = 1
    #        apply_method = "pending-reboot"
    #    }
  ]
}


web_target_groups = [
  {
    name               = "DXCORE-API-SSL"
    port               = 7503
    protocol           = "TCP"
    preserve_client_ip = true
  },
  {
    name               = "DXCORE-API"
    port               = 7500
    protocol           = "TCP"
    preserve_client_ip = true
  },
  {
    name               = "DXCORE-API-MUX-OUT"
    port               = 7501
    protocol           = "TCP"
    preserve_client_ip = true
  },
  {
    name               = "QUOTE-DATA"
    port               = 7800
    protocol           = "TCP"
    preserve_client_ip = true
  }
]
app_target_groups = [
  {
    name               = "HTTPS" # Port Name
    port               = 443     # Port Number
    protocol           = "TCP"   # Protocol Type
    preserve_client_ip = true    # Preserve remote client IP
  }
]