# CHANGE ME
required_tags = {
  Project   = "Pepperstone"
  Platform  = "devci"
  Env       = "dev"
  ManagedBy = "terraform"
}

aws_external_domain = "dev.oms.pepperstone.com"
aws_local_domain    = "us.dev.internal.pepperstone.com"

# security setting for ingress, for outgoing rules you will need to extend the config to require them
# for more info refer to security module and it's documentation
security_groups = {
  ingress_cidr_blocks = []
  ingress_with_cidr_blocks = [
    # ICMP questionable, but use full - denied does not meet compliance standards changed to internal ranges only
    {
      from_port   = -1
      to_port     = -1
      protocol    = "icmp"
      description = "User-service ports (ipv4)"
      cidr_blocks = "**********/22"
    },
    #SSH access , questionable, but to be double sure we have fail2ban installation as a default in our playbooks
    {
      from_port   = 22
      to_port     = 22
      protocol    = "tcp"
      description = "User-service ports (ipv4)"
      cidr_blocks = "**********/22"
    },
    # VPC network - denied too permissive
    {
      from_port   = 0
      to_port     = 65535
      protocol    = "TCP"
      description = "VPC network"
      cidr_blocks = "**********/22"
    },
    {
      from_port   = 0
      to_port     = 65535
      protocol    = "UDP"
      description = "VPC network"
      cidr_blocks = "**********/22"
    },

    ####### CLIENT BLOCK
    {
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      description = "HTTP"
      cidr_blocks = "0.0.0.0/0"
    },
    {
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      description = "HTTPS"
      cidr_blocks = "0.0.0.0/0"
    },
    {
      from_port   = 7500
      to_port     = 7503
      protocol    = "TCP"
      description = "DXCORE API"
      cidr_blocks = "0.0.0.0/0"
    },
    {
      from_port   = 7800
      to_port     = 7800
      protocol    = "TCP"
      description = "QUOTE DATA"
      cidr_blocks = "0.0.0.0/0"
    }
  ]
  ingress_rules = []
  egress_rules = [
    "all-all"
  ]
}

app = {
  name                        = "devci-app",
  instance_type               = "m5a.4xlarge",
  count                       = 1,
  ami                         = "ami-0eacec0bf55b70bc9",
  associate_public_ip_address = false
  eip                         = false
  ebs_optimized               = false
  root_block_device = [
    {
      volume_type = "gp3"
      volume_size = 50
    }
  ]
  ebs_block_device = [
    {
      device_name = "/dev/sdb"
      volume_type = "gp3"
      volume_size = 150
    }
  ]
  cloud_init = "./cloud_init/_cloud_init_agent_app.yml"
  tags = {
    "allocation" = "app"
  }
}

web = {
  name                        = "devci-web",
  instance_type               = "m5a.2xlarge",
  count                       = 1,
  ami                         = "ami-0eacec0bf55b70bc9", //Oracle Linux ami-0eacec0bf55b70bc9 - us-east-1
  associate_public_ip_address = false
  eip                         = false
  ebs_optimized               = false
  root_block_device = [
    {
      volume_type = "gp3"
      volume_size = 50
    }
  ]
  ebs_block_device = [
    {
      device_name = "/dev/sdb"
      volume_type = "gp3"
      volume_size = 150
    }
  ]
  cloud_init = "./cloud_init/_cloud_init_agent_web.yml"
  tags = {
    "allocation" = "web"
  }
}

util = {
  name                        = "devci-util",
  instance_type               = "t3.medium",
  count                       = 3,
  ami                         = "ami-0eacec0bf55b70bc9",
  associate_public_ip_address = false
  eip                         = false
  ebs_optimized               = false
  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 30
    }
  ]
  ebs_block_device = [
    {
      device_name = "/dev/sdb"
      volume_type = "gp3"
      volume_size = 150
    }
  ]

  cloud_init = "./cloud_init/_cloud_init_server.yml"

  tags = {
  }
}

elk = {
  name                        = "devci-elk",
  instance_type               = "t3.medium",
  count                       = 1,
  ami                         = "ami-0eacec0bf55b70bc9",
  associate_public_ip_address = false
  eip                         = false
  ebs_optimized               = false
  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 30
    }
  ]
  ebs_block_device = [
    {
      device_name = "/dev/sdb"
      volume_type = "gp3"
      volume_size = 150
    }
  ]
  cloud_init = "./cloud_init/_cloud_init_elk.yml"

  tags = {
  }
}

zabbix = {
  name                        = "devci-zabbix",
  instance_type               = "t3.medium",
  count                       = 1,
  ami                         = "ami-0eacec0bf55b70bc9",
  associate_public_ip_address = false
  eip                         = false
  ebs_optimized               = false
  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 30
    }
  ]
  ebs_block_device = [
    {
      device_name = "/dev/sdb"
      volume_type = "gp3"
      volume_size = 150
    }
  ]
  cloud_init = "./cloud_init/_cloud_init_agent_monitoring.yml"

  tags = {
  }
}


# CHANGE ME
# basic RDS config
rds = {
  # CHANGE ME
  name = "devci-postgresql"
  # this should be "aurora-postgresql" , because, the engine in AWS is named this way
  engine         = "aurora-postgresql"
  engine_version = "12.12"
  instances = {
    0 = {
      identifier = "single-postgresql-1"
    }
  }
  instance_type              = "db.r5.large"
  auto_minor_version_upgrade = false
  shared_preload_libraries   = "pg_stat_statements,pg_hint_plan,pg_cron"
  deletion_protection        = false
}

rds_params = {
  ordinary_group : [
    {
      name         = "max_locks_per_transaction"
      value        = "2048"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_pred_locks_per_transaction"
      value        = "8192"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_connections"
      value        = "512"
      apply_method = "pending-reboot"
    },
    {
      name         = "pg_stat_statements.track"
      value        = "all"
      apply_method = "immediate"
    },
    {
      name         = "track_activity_query_size"
      value        = "8192"
      apply_method = "pending-reboot"
    },
    {
      name         = "track_io_timing"
      value        = "1"
      apply_method = "immediate"
    },
    {
      name         = "pg_stat_statements.max"
      value        = "5000"
      apply_method = "pending-reboot"
    },
    {
      name         = "auto_explain.log_min_duration"
      value        = "-1"
      apply_method = "immediate"
    },
    {
      name         = "auto_explain.log_nested_statements"
      value        = "0"
      apply_method = "immediate"
    },
    {
      name         = "cron.database_name"
      value        = "dxcore"
      apply_method = "pending-reboot"
    }


    # Minimum statement execution time, in milliseconds.
    # If you set it to 250ms then all statements that run 250ms or longer will be logged. Setting this to 0 logs all plans.
    #    {
    #        name  = "auto_explain.log_min_duration"
    #        value =  5000
    #        apply_method = "pending-reboot"
    #    },
    #    # Causes nested statements (statements executed inside a function) to be considered for logging. When it is off, only top-level query plans are logged
    #    {
    #        name  = "auto_explain.log_nested_statements"
    #        value = 1
    #        apply_method = "pending-reboot"
    #    }
  ],
  cluster_group : [
    {
      name         = "max_locks_per_transaction"
      value        = "2048"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_pred_locks_per_transaction"
      value        = "8192"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_connections"
      value        = "512"
      apply_method = "pending-reboot"
    },
    {
      name         = "track_activity_query_size"
      value        = "8192"
      apply_method = "pending-reboot"
    },
    {
      name         = "track_io_timing"
      value        = "1"
      apply_method = "immediate"
    },
    {
      name         = "pg_stat_statements.max"
      value        = "5000"
      apply_method = "pending-reboot"
    },
    {
      name         = "pg_stat_statements.track"
      value        = "all"
      apply_method = "immediate"
    },
    {
      name         = "auto_explain.log_min_duration"
      value        = "-1"
      apply_method = "immediate"
    },
    {
      name         = "auto_explain.log_nested_statements"
      value        = "0"
      apply_method = "immediate"
    },
    {
      name         = "synchronous_commit"
      value        = "on"
      apply_method = "immediate"
    },
    {
      name         = "rds.logical_replication"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "cron.database_name"
      value        = "dxcore"
      apply_method = "pending-reboot"
    }

    # Minimum statement execution time, in milliseconds.
    # If you set it to 250ms then all statements that run 250ms or longer will be logged. Setting this to 0 logs all plans.
    #    {
    #        name  = "auto_explain.log_min_duration"
    #        value =  5000
    #        apply_method = "pending-reboot"
    #    },
    #    # Causes nested statements (statements executed inside a function) to be considered for logging. When it is off, only top-level query plans are logged
    #    {
    #        name  = "auto_explain.log_nested_statements"
    #        value = 1
    #        apply_method = "pending-reboot"
    #    }
  ]
}

web_target_groups = [
  {
    name               = "HTTPS" # Port Name
    port               = 443     # Port Number
    protocol           = "TCP"   # Protocol Type
    preserve_client_ip = true    # Preserve remote client IP
    client_index       = 1       # Instance index to point target to
}]
app_target_groups = [
  {
    name               = "DXCORE-API-SSL"
    port               = 7503
    protocol           = "TCP"
    preserve_client_ip = true
    client_index       = 0
  },
  {
    name               = "DXCORE-API"
    port               = 7500
    protocol           = "TCP"
    preserve_client_ip = true
    client_index       = 0
  },
  {
    name               = "DXCORE-API-MUX-OUT"
    port               = 7501
    protocol           = "TCP"
    preserve_client_ip = true
    client_index       = 0
  },
  {
    name               = "QUOTE-DATA"
    port               = 7800
    protocol           = "TCP"
    preserve_client_ip = true
    client_index       = 0
  }
]

s3_ssl_bucket_name = "psc-manual-s3-ssl-255099534900"
s3_ssl_file_cert   = "devci.dev.oms.pepperstone.com/cert.pem"
s3_ssl_file_key    = "devci.dev.oms.pepperstone.com/privatekey.pem"
