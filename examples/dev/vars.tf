variable "security_groups" {
  type    = any
  default = {}
}

variable "required_tags" {
  type    = any
  default = {}
}

variable "rds" {
  type    = any
  default = {}
}

variable "rds_params" {
  type    = any
  default = { ordinary_group : [], cluster_group : [] }
}

variable "util" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}

variable "app" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}

variable "web" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}

variable "zabbix" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}


variable "elk" {
  type = object({
    name                        = string,
    instance_type               = string,
    count                       = number,
    ami                         = string,
    associate_public_ip_address = bool
    eip                         = bool
    root_block_device           = list(any)
    ebs_block_device            = list(any)
    cloud_init                  = string
    tags                        = map(string)
    ebs_optimized               = bool
  })
}


variable "app_target_groups" {
  type    = list(any)
  default = []
}

variable "web_target_groups" {
  type    = list(any)
  default = []
}

variable "aws_local_domain" {
  type        = string
  default     = "aws.local"
  description = "main domain for route_53"
}

variable "aws_external_domain" {
  type        = string
  description = "external domain for route_53"
}

variable "s3_ssl_bucket_name" {
  description = "Manual s3 bucket name for ssl certificates to be pulled from."
  type        = string
  default     = ""
}

variable "s3_ssl_file_cert" {
  description = "Manual s3 cert file location."
  type        = string
  default     = ""
}

variable "s3_ssl_file_key" {
  description = "Manual s3 cert file location."
  type        = string
  default     = ""
}