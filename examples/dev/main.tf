provider "aws" {
  # Configuration options
  region = "us-east-1"

  default_tags {
    tags = {
      Owner       = "DevOps"
      Environment = "dev"
      ManagedBy   = "terraform"
      Repo        = "dxtrade"
      Product     = "oms"
      Domain      = "x2"
    }
  }
}

module "devci" {
  source              = "../../terraform"
  deployment_env      = "dev1"
  app                 = var.app
  web                 = var.web
  util                = var.util
  elk                 = var.elk
  zabbix              = var.zabbix
  app_target_groups   = var.app_target_groups
  web_target_groups   = var.web_target_groups
  aws_external_domain = var.aws_external_domain
  aws_local_domain    = var.aws_local_domain
  required_tags       = var.required_tags
  security_groups     = var.security_groups
  rds                 = var.rds
  rds_params          = var.rds_params
  vpc_name            = "dev-01-vpc"
  # subnet_db           = "dev-01-vpc-db-*"
  subnet_public      = "dev-01-vpc-public-*"
  subnet_private     = "dev-01-vpc-private-*"
  s3_ssl_bucket_name = var.s3_ssl_bucket_name
  s3_ssl_file_cert   = var.s3_ssl_file_cert
  s3_ssl_file_key    = var.s3_ssl_file_key
}