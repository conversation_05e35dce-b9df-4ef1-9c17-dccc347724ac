# DXTrade Terraform module

This repository contains a terraform module for the creation and management of dxtrade systems based off the code provided by DevExperts upgraded to be in line with Pepperstone best practices 

## Limitations

Terraform 1.1.7 is required to make this module work.

## Requirements

1. Docker
1. Docker-compose
1. LocalStack Pro API Key (should be set as LOCALSTACK_API_KEY in your environment)
1. Make

## Testing changes

Before pushing and raising a PR tests should be run locally, the following steps descorbe how to make the changes:

1. Open Terminal
1. Set LOCASTACK_API_KEY in env: `export LOCALSTACK_API_KEY=<your key here>`
1. make ci

Once a PR is raised your changes will also be tested by GH Actions, these must pass in order for the PR to be merged successfully.

## Contributing

Contrbutions should be made in the form of a pull request.  A template for PR is available and should be used wherever possible. 
<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

No providers.

## Modules

No modules.

## Resources

No resources.

## Inputs

No inputs.

## Outputs

No outputs.
<!-- END_TF_DOCS -->