package test

import (
	"context"
	"log"
	"strconv"
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/aws-sdk-go-v2/service/route53"
	route53types "github.com/aws/aws-sdk-go-v2/service/route53/types"
	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

func createVpc(ec2svc *ec2.Client, cidr string, tags []types.Tag) *string {
	input := &ec2.CreateVpcInput{
		CidrBlock: aws.String(cidr),
	}

	resp, err := ec2svc.CreateVpc(context.Background(), input)
	if err != nil {
		log.Fatalf("Error while creating VPC: %s", err)
	}
	prod := resp.Vpc.VpcId

	tagInput := &ec2.CreateTagsInput{
		Resources: []string{*prod},
		Tags:      tags,
	}

	_, err = ec2svc.CreateTags(context.TODO(), tagInput)
	if err != nil {
		log.Fatalf("Got an error tagging the instance: %s", err)
	}
	log.Printf("Prod Id is %s", *prod)
	return prod
}

func createSubnets(ec2svc *ec2.Client, vpc_id string, cidr_prefix string) []string {
	private_subnets := []string{"0", "32", "64", "96"}
	public_subnets := []string{"128", "160", "192", "224"}
	var subnets []string
	for i, s := range private_subnets {
		cidr_block := cidr_prefix + ".0." + s + "/27"
		tags := []types.Tag{
			{
				Key:   aws.String("Environment"),
				Value: aws.String("np"),
			},
			{
				Key:   aws.String("Name"),
				Value: aws.String("dxtrade-np-private-" + strconv.Itoa(i)),
			},
		}
		input := &ec2.CreateSubnetInput{
			VpcId:     &vpc_id,
			CidrBlock: &cidr_block,
		}

		subnet, err := ec2svc.CreateSubnet(context.Background(), input)
		tagInput := &ec2.CreateTagsInput{
			Resources: []string{*subnet.Subnet.SubnetId},
			Tags:      tags,
		}

		_, err = ec2svc.CreateTags(context.TODO(), tagInput)
		if err != nil {
			log.Fatalf("Got an error tagging the instance: %s", err)
		}
		subnets = append(subnets, *subnet.Subnet.SubnetId)
		log.Printf("Create Subnet: %s", cidr_block)
	}
	for i, s := range public_subnets {
		cidr_block := cidr_prefix + ".0." + s + "/27"
		tags := []types.Tag{
			{
				Key:   aws.String("Environment"),
				Value: aws.String("np"),
			},
			{
				Key:   aws.String("Name"),
				Value: aws.String("dxtrade-np-public-" + strconv.Itoa(i)),
			},
		}
		input := &ec2.CreateSubnetInput{
			VpcId:     &vpc_id,
			CidrBlock: &cidr_block,
		}

		subnet, err := ec2svc.CreateSubnet(context.Background(), input)
		tagInput := &ec2.CreateTagsInput{
			Resources: []string{*subnet.Subnet.SubnetId},
			Tags:      tags,
		}

		_, err = ec2svc.CreateTags(context.TODO(), tagInput)
		if err != nil {
			log.Fatalf("Got an error tagging the instance: %s", err)
		}
		subnets = append(subnets, *subnet.Subnet.SubnetId)
		log.Printf("Create Subnet: %s", cidr_block)
	}
	return subnets
}

func setupAWS() (aws.Config, aws.Config) {
	//setup Prebuild AWS resources
	awsEndpoint := "http://localstack.test:4566"

	customResolverUS := aws.EndpointResolverFunc(func(service, region string) (aws.Endpoint, error) {
		if awsEndpoint != "" {
			return aws.Endpoint{
				PartitionID:   "aws",
				URL:           awsEndpoint,
				SigningRegion: "us-east-1",
			}, nil
		}

		// returning EndpointNotFoundError will allow the service to fallback to it's default resolution
		return aws.Endpoint{}, &aws.EndpointNotFoundError{}
	})
	customResolverUK := aws.EndpointResolverFunc(func(service, region string) (aws.Endpoint, error) {
		if awsEndpoint != "" {
			return aws.Endpoint{
				PartitionID:   "aws",
				URL:           awsEndpoint,
				SigningRegion: "eu-west-2",
			}, nil
		}

		// returning EndpointNotFoundError will allow the service to fallback to it's default resolution
		return aws.Endpoint{}, &aws.EndpointNotFoundError{}
	})

	awsCfgUS, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion("us-east-1"),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider("xxx", "xxx", "xxx")),
		config.WithEndpointResolver(customResolverUS),
	)
	if err != nil {
		log.Fatalf("Cannot load the AWS configs: %s", err)
	}
	awsCfgUK, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion("eu-west-2"),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider("xxx", "xxx", "xxx")),
		config.WithEndpointResolver(customResolverUK),
	)
	if err != nil {
		log.Fatalf("Cannot load the AWS configs: %s", err)
	}

	return awsCfgUS, awsCfgUK
}

func setup() ([][]string, [][]string, string, []string) {
	awsCfgUS, awsCfgUK := setupAWS()
	//Create Prod VPC
	ec2svcUS := ec2.NewFromConfig(awsCfgUS)
	ec2svcUK := ec2.NewFromConfig(awsCfgUK)
	prodTags := []types.Tag{
		{
			Key:   aws.String("Environment"),
			Value: aws.String("np"),
		},
		{
			Key:   aws.String("Name"),
			Value: aws.String("dxtrade-np"),
		},
	}
	prefixUS := "10.210"
	prefixUK := "10.211"
	prodUS := createVpc(ec2svcUS, prefixUS+".0.0/24", prodTags)
	prodUK := createVpc(ec2svcUK, prefixUK+".0.0/24", prodTags)
	ussubnets := createSubnets(ec2svcUS, *prodUS, prefixUS)
	uksubnets := createSubnets(ec2svcUK, *prodUK, prefixUK)
	us := [][]string{[]string{*prodUS}, ussubnets}
	uk := [][]string{[]string{*prodUK}, uksubnets}
	r53svc := route53.NewFromConfig(awsCfgUS)
	domain := "np.oms.pepperstone.com"
	input := &route53.CreateHostedZoneInput{
		CallerReference: &domain,
		Name:            &domain,
	}
	ext_domain, err := r53svc.CreateHostedZone(context.Background(), input)
	if err != nil {
		log.Fatalf("Cannot load the AWS configs: %s", err)
	}
	usdomain := "us.np.internal.pepperstone.com"
	config := route53types.HostedZoneConfig{
		PrivateZone: true,
		Comment:     &domain,
	}
	usvpc := route53types.VPC{
		VPCId:     &us[0][0],
		VPCRegion: route53types.VPCRegionUsEast1,
	}
	input = &route53.CreateHostedZoneInput{
		CallerReference:  &usdomain,
		Name:             &usdomain,
		HostedZoneConfig: &config,
		VPC:              &usvpc,
	}
	us_domain, err := r53svc.CreateHostedZone(context.Background(), input)
	if err != nil {
		log.Fatalf("Cannot load the AWS configs: %s", err)
	}
	ukdomain := "uk.np.internal.pepperstone.com"
	ukvpc := route53types.VPC{
		VPCId:     &uk[0][0],
		VPCRegion: route53types.VPCRegionEuWest2,
	}
	input = &route53.CreateHostedZoneInput{
		CallerReference:  &ukdomain,
		Name:             &ukdomain,
		HostedZoneConfig: &config,
		VPC:              &ukvpc,
	}
	uk_domain, err := r53svc.CreateHostedZone(context.Background(), input)
	if err != nil {
		log.Fatalf("Cannot load the AWS configs: %s", err)
	}
	int_domains := []string{*us_domain.HostedZone.Id, *uk_domain.HostedZone.Id}
	return us, uk, *ext_domain.HostedZone.Id, int_domains
}

func teardownRegion(region [][]string, ec2svc *ec2.Client) {

	for i, s := range region {
		if i == 0 {
			for _, v := range s {
				input := &ec2.DeleteVpcInput{
					VpcId: &v,
				}

				ec2svc.DeleteVpc(context.Background(), input)
				log.Printf("Delete VPC: %s", v)
			}
		} else {
			for _, sub := range s {
				input := &ec2.DeleteSubnetInput{
					SubnetId: &sub,
				}

				ec2svc.DeleteSubnet(context.Background(), input)
				log.Printf("Delete Subnet: %s", sub)
			}
		}
	}
}

func teardown(t *testing.T, terraformOptions terraform.Options, uk [][]string, us [][]string, ext_domain string, int_domains []string) {
	terraform.Destroy(t, &terraformOptions)
	awsCfgUS, awsCfgUK := setupAWS()
	ec2svcUS := ec2.NewFromConfig(awsCfgUS)
	ec2svcUK := ec2.NewFromConfig(awsCfgUK)
	// iterate over vpc's and delete
	teardownRegion(us, ec2svcUS)
	teardownRegion(uk, ec2svcUK)
	r53svc := route53.NewFromConfig(awsCfgUS)
	input := &route53.DeleteHostedZoneInput{
		Id: &ext_domain,
	}
	r53svc.DeleteHostedZone(context.Background(), input)

	for _, domain := range int_domains {
		input := &route53.DeleteHostedZoneInput{
			Id: &domain,
		}
		r53svc.DeleteHostedZone(context.Background(), input)
	}
}

func TestDefault(t *testing.T) {
	us, uk, ext_domain, int_domains := setup()
	// setup Terraform context for testing the module
	// TerraformDir should point at your test use case for the module.
	// Vars should be a map of any variables you need to pass into the setup
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		TerraformDir: "./verify_setup",
		Vars:         map[string]interface{}{},
	})

	terraform.InitAndApply(t, terraformOptions)

	us_vpc_id := terraform.Output(t, terraformOptions, "us_vpc_id")
	if assert.NotNil(t, us_vpc_id) {
		assert.Equal(t, us_vpc_id, us[0][0])
	}

	uk_vpc_id := terraform.Output(t, terraformOptions, "uk_vpc_id")
	if assert.NotNil(t, uk_vpc_id) {
		assert.Equal(t, uk_vpc_id, uk[0][0])
	}

	external_domain := terraform.Output(t, terraformOptions, "external_domain")
	if assert.NotNil(t, external_domain) {
		assert.Equal(t, "/hostedzone/"+external_domain, ext_domain)
	}
	// teardown terraform resources after completion to ensure each run is clean
	defer teardown(t, *terraformOptions, uk, us, ext_domain, int_domains)
}

func TestSingleAZSetup(t *testing.T) {
	us, uk, ext_domain, int_domains := setup()
	// setup Terraform context for testing the module
	// TerraformDir should point at your test use case for the module.
	// Vars should be a map of any variables you need to pass into the setup
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		TerraformDir: "./single_az_single_setup",
		Vars:         map[string]interface{}{},
	})

	terraform.InitAndApply(t, terraformOptions)

	// teardown terraform resources after completion to ensure each run is clean
	defer teardown(t, *terraformOptions, uk, us, ext_domain, int_domains)
}
