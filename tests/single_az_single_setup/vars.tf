variable "security_groups" {
  type    = any
  default = {}
}

variable "required_tags" {
  type    = any
  default = {}
}

variable "rds" {
  type    = any
  default = {}
}

variable "rds_params" {
  type    = any
  default = { ordinary_group : [], cluster_group : [] }
}

variable "ebs_devices" {
  type    = list(any)
  default = []
}
variable "instances" {
  type    = list(any)
  default = []
}

variable "target_groups" {
  type    = list(any)
  default = []
}
variable "profile" {
  default = ""
}

variable "aws_local_domain" {
  type        = string
  default     = "aws.local"
  description = "main domain for route_53"
}

variable "aws_external_domain" {
  type        = string
  description = "external domain for route_53"
}

variable "records" {
  type    = list(any)
  default = []
}